package user

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo/options"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *service) UpdateUser(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	user, err := s.GetUserByID(ctx, userID)
	if err != nil || user == nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if updateUserInput == nil {
		return user, nil
	}

	if updateUserInput.Name != nil {
		user.Name = updateUserInput.Name
	}
	if updateUserInput.ProfileImageURL != nil {
		user.ProfileImageURL = updateUserInput.ProfileImageURL
	}
	if updateUserInput.CountryCode != nil {
		user.CountryCode = updateUserInput.CountryCode
	}
	if updateUserInput.Timezone != nil {
		user.Timezone = updateUserInput.Timezone
	}
	if updateUserInput.Bio != nil {
		user.Bio = updateUserInput.Bio
	}
	if updateUserInput.Username != nil {
		isAvailable, _ := s.IsUsernameAvailable(ctx, updateUserInput.Username)
		if isAvailable {
			user.Username = *updateUserInput.Username
		}
	}
	if updateUserInput.Country != nil {
		user.Country = updateUserInput.Country
	}
	if updateUserInput.AwardsAndAchievements != nil {
		if len(updateUserInput.AwardsAndAchievements) > 100 {
			updateUserInput.AwardsAndAchievements = updateUserInput.AwardsAndAchievements[:100]
			zlog.Warn(ctx, "max allowed 100 awards")
		}
		awardsAndAchievements := make([]*models.AwardsAndAchievements, len(updateUserInput.AwardsAndAchievements))
		for i, awardAndAchievement := range updateUserInput.AwardsAndAchievements {
			awardsAndAchievements[i] = &models.AwardsAndAchievements{
				ImageURL:    awardAndAchievement.ImageURL,
				Link:        awardAndAchievement.Link,
				Title:       awardAndAchievement.Title,
				Description: awardAndAchievement.Description,
			}
		}
		user.AwardsAndAchievements = awardsAndAchievements
	}
	if updateUserInput.Links != nil {
		user.Links = updateUserInput.Links
	}

	if updateUserInput.InstitutionID != nil {
		institution, err := s.institutionRepo.FindByID(ctx, *updateUserInput.InstitutionID)
		if err != nil || institution == nil {
			zlog.Error(ctx, "Failed to update user institution during profile update", err)
			return nil, fmt.Errorf("invalid institution ID: %w", err)
		} else {
			user.InstitutionID = updateUserInput.InstitutionID
			user.InstitutionName = &institution.Name
		}
	}

	err = s.UpdateUserFromObject(ctx, user)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func (s *service) UpdateUserFromObject(ctx context.Context, user *models.User) error {
	filter := bson.M{"_id": user.ID}
	update := bson.M{"$set": user}
	if len(user.AwardsAndAchievements) == 0 {
		update["$unset"] = bson.M{"awardsAndAchievements": 1}
	}

	return s.userRepo.UpdateOne(ctx, filter, update)
}

func (s *service) UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error {
	return s.userRepo.UpdateOne(ctx, filter, update, opts...)
}
