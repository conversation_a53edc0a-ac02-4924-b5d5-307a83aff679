package user

import (
	"context"
	"errors"
	"regexp"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (s *service) SearchUsersInMyInstitute(ctx context.Context, searchKey *string, page, pageSize *int) (*models.MyInstituteUsersPage, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	currentUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get current user details", err, zap.String("userID", userID.Hex()))
		return nil, errors.New("failed to retrieve user information")
	}
	if currentUser == nil {
		return nil, errors.New("user not found")
	}

	if currentUser.InstitutionID == nil {
		return &models.MyInstituteUsersPage{
			Results:      []*models.SearchUserOutput{},
			PageNumber:   1,
			PageSize:     0,
			HasMore:      false,
			TotalResults: 0,
		}, nil
	}

	filter := bson.M{
		"institutionId": currentUser.InstitutionID,
		"_id":           bson.M{"$ne": userID},
		"isGuest":       false,
	}

	if searchKey != nil && *searchKey != "" {
		escapedSearchKey := regexp.QuoteMeta(*searchKey)
		regexFilter := bson.M{"$regex": primitive.Regex{Pattern: escapedSearchKey, Options: "i"}}

		filter["$or"] = []bson.M{
			{"username": regexFilter},
			{"name": regexFilter},
		}
	}

	var currentPage int64 = 1
	var currentPageSize int64 = DefaultPageSize

	if page != nil && *page > 0 {
		currentPage = int64(*page)
	}
	if pageSize != nil && *pageSize > 0 {
		currentPageSize = int64(*pageSize)
	}

	skip := (currentPage - 1) * currentPageSize
	limit := currentPageSize

	opts := options.Find().
		SetSort(bson.D{{Key: "username", Value: 1}}).
		SetSkip(skip).
		SetLimit(limit)

	users, err := s.userRepo.Find(ctx, filter, opts)
	if err != nil {
		zlog.Error(ctx, "Failed to search paginated users by institute ID and key", err,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.Stringp("searchKey", searchKey),
			zap.String("userID", userID.Hex()),
		)
		return nil, errors.New("failed to search users in your institute")
	}

	// Extract user IDs for bulk friendship status check
	var targetUserIds []primitive.ObjectID
	for _, user := range users {
		targetUserIds = append(targetUserIds, user.ID)
	}

	// Get bulk friendship status for all users in one query
	friendshipStatuses, err := s.friendsAndFollowersRepo.GetBulkFriendshipStatus(ctx, userID, targetUserIds)
	if err != nil {
		zlog.Error(ctx, "Failed to get bulk friendship status", err,
			zap.String("currentUserID", userID.Hex()),
			zap.Int("targetUsersCount", len(targetUserIds)),
		)
		// Continue with default values if bulk check fails
		friendshipStatuses = make(map[string]*models.UserFriendshipStatus)
		for _, userIDObj := range targetUserIds {
			friendshipStatuses[userIDObj.Hex()] = &models.UserFriendshipStatus{
				UserID:           userIDObj,
				IsFollowing:      false,
				FriendshipStatus: models.FriendshipStatusNotFriend,
			}
		}
	}

	searchUserOutputs := make([]*models.SearchUserOutput, len(users))
	for i, user := range users {
		status := friendshipStatuses[user.ID.Hex()]
		if status == nil {
			status = &models.UserFriendshipStatus{
				UserID:           user.ID,
				IsFollowing:      false,
				FriendshipStatus: models.FriendshipStatusNotFriend,
			}
		}

		searchUserOutputs[i] = &models.SearchUserOutput{
			UserPublicDetails: user,
			IsFollowing:       &status.IsFollowing,
			FriendshipStatus:  &status.FriendshipStatus,
		}
	}

	totalCount, countErr := s.userRepo.CountDocuments(ctx, filter)
	if countErr != nil {
		zlog.Error(ctx, "Failed to count users by institute ID and search key", countErr,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.Stringp("searchKey", searchKey),
			zap.String("userID", userID.Hex()),
		)
		totalCount = 0
	}

	hasMore := false
	if totalCount > 0 {
		hasMore = (skip + limit) < totalCount
	}

	return &models.MyInstituteUsersPage{
		Results:      searchUserOutputs,
		PageNumber:   currentPage,
		PageSize:     currentPageSize,
		HasMore:      hasMore,
		TotalResults: totalCount,
	}, nil
}
