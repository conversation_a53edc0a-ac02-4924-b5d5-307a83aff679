package user

import (
	"context"
	"errors"
	"regexp"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (s *service) SearchUsersInMyInstitute(ctx context.Context, searchKey *string, page, pageSize *int) (*models.MyInstituteUsersPage, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	currentUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get current user details", err, zap.String("userID", userID.Hex()))
		return nil, errors.New("failed to retrieve user information")
	}
	if currentUser == nil {
		return nil, errors.New("user not found")
	}

	if currentUser.InstitutionID == nil {
		return &models.MyInstituteUsersPage{
			Results:      []*models.SearchUserOutput{},
			PageNumber:   1,
			PageSize:     0,
			HasMore:      false,
			TotalResults: 0,
		}, nil
	}

	filter := bson.M{
		"institutionId": currentUser.InstitutionID,
		"_id":           bson.M{"$ne": userID},
		"isGuest":       false,
	}

	if searchKey != nil && *searchKey != "" {
		escapedSearchKey := regexp.QuoteMeta(*searchKey)
		regexFilter := bson.M{"$regex": primitive.Regex{Pattern: escapedSearchKey, Options: "i"}}

		filter["$or"] = []bson.M{
			{"username": regexFilter},
			{"name": regexFilter},
		}
	}

	var currentPage int64 = 1
	var currentPageSize int64 = DefaultPageSize

	if page != nil && *page > 0 {
		currentPage = int64(*page)
	}
	if pageSize != nil && *pageSize > 0 {
		currentPageSize = int64(*pageSize)
	}

	skip := (currentPage - 1) * currentPageSize
	limit := currentPageSize

	opts := options.Find().
		SetSort(bson.D{{Key: "username", Value: 1}}).
		SetSkip(skip).
		SetLimit(limit)

	users, err := s.userRepo.Find(ctx, filter, opts)
	if err != nil {
		zlog.Error(ctx, "Failed to search paginated users by institute ID and key", err,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.Stringp("searchKey", searchKey),
			zap.String("userID", userID.Hex()),
		)
		return nil, errors.New("failed to search users in your institute")
	}

	searchUserOutputs := make([]*models.SearchUserOutput, len(users))
	for i, user := range users {
		// Get friendship status for each user
		isFollowing, friendshipStatus, err := s.friendsAndFollowersRepo.CheckFollowingAndFriendsStatusConcurrent(ctx, userID, user.ID)
		if err != nil {
			zlog.Error(ctx, "Failed to check friendship status", err,
				zap.String("currentUserID", userID.Hex()),
				zap.String("targetUserID", user.ID.Hex()),
			)
			// Continue with default values if friendship check fails
			isFollowing = false
			friendshipStatus = models.FriendshipStatusNotFriend
		}

		searchUserOutputs[i] = &models.SearchUserOutput{
			UserPublicDetails: user,
			IsFollowing:       &isFollowing,
			FriendshipStatus:  &friendshipStatus,
		}
	}

	totalCount, countErr := s.userRepo.CountDocuments(ctx, filter)
	if countErr != nil {
		zlog.Error(ctx, "Failed to count users by institute ID and search key", countErr,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.Stringp("searchKey", searchKey),
			zap.String("userID", userID.Hex()),
		)
		totalCount = 0
	}

	hasMore := false
	if totalCount > 0 {
		hasMore = (skip + limit) < totalCount
	}

	return &models.MyInstituteUsersPage{
		Results:      searchUserOutputs,
		PageNumber:   currentPage,
		PageSize:     currentPageSize,
		HasMore:      hasMore,
		TotalResults: totalCount,
	}, nil
}
