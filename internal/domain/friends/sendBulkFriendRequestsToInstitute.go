package friends

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) SendBulkFriendRequestsToInstitute(ctx context.Context) (int, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return 0, err
	}

	currentUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get current user details", err, zap.String("userID", userID.Hex()))
		return 0, errors.New("failed to retrieve user information")
	}
	if currentUser == nil {
		return 0, errors.New("user not found")
	}

	if currentUser.InstitutionID == nil {
		return 0, errors.New("user is not part of any institution")
	}

	filter := bson.M{
		"institutionId": currentUser.InstitutionID,
		"_id":           bson.M{"$ne": userID},
		"isGuest":       false,
	}

	users, err := s.userRepo.Find(ctx, filter, nil)
	if err != nil {
		zlog.Error(ctx, "Failed to find users by institute ID", err,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.String("userID", userID.Hex()),
		)
		return 0, errors.New("failed to retrieve users from your institute")
	}

	if len(users) == 0 {
		return 0, nil
	}

	var targetUserIds []primitive.ObjectID
	for _, user := range users {
		targetUserIds = append(targetUserIds, user.ID)
	}

	err = s.friendsRepo.CreateBulkFriendRequests(ctx, userID, targetUserIds)
	if err != nil {
		zlog.Error(ctx, "Failed to create bulk friend requests", err,
			zap.String("currentUserID", userID.Hex()),
			zap.Int("targetUsersCount", len(targetUserIds)),
		)
		return 0, errors.New("failed to send friend requests")
	}

	friendshipStatuses, err := s.friendsRepo.GetBulkFriendshipStatus(ctx, userID, targetUserIds)
	if err != nil {
		zlog.Error(ctx, "Failed to get friendship statuses after bulk request", err)
		return len(targetUserIds), nil
	}

	sentCount := 0
	for _, status := range friendshipStatuses {
		if status.FriendshipStatus == models.FriendshipStatusRequestSent {
			sentCount++
		}
	}

	zlog.Info(ctx, "Bulk friend requests sent successfully",
		zap.String("userID", userID.Hex()),
		zap.String("institutionID", currentUser.InstitutionID.Hex()),
		zap.Int("totalUsers", len(users)),
		zap.Int("requestsSent", sentCount),
	)

	return sentCount, nil
}
