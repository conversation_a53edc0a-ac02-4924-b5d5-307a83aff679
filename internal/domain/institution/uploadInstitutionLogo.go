package institution

import (
	"context"
	"fmt"
	"io"
	"time"

	"cloud.google.com/go/storage"
	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) UploadInstitutionLogo(ctx context.Context, file graphql.Upload, institutionID primitive.ObjectID) (*models.File, error) {
	if institutionID == primitive.NilObjectID {
		return nil, fmt.Errorf("institution ID cannot be empty")
	}

	_, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ID: %w", err)
	}

	institution, err := s.instituteRepo.FindByID(ctx, institutionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get institution: %w", err)
	}

	if institution == nil {
		return nil, fmt.Errorf("institution not found")
	}

	// TODO: Add authorization check - for now, any authenticated user can upload

	bucket := s.storage.Storage.Bucket(constants.StorageBucket)
	objectName := "institution_logo/" + institutionID.Hex() + "_institution-logo.jpeg"
	object := bucket.Object(objectName)

	if file.File == nil {
		return nil, fmt.Errorf("file cannot be nil")
	}

	content, err := io.ReadAll(file.File)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	writer := object.NewWriter(ctx)
	writer.ObjectAttrs.ContentType = file.ContentType
	writer.ObjectAttrs.ACL = []storage.ACLRule{{Entity: storage.AllUsers, Role: storage.RoleReader}}
	writer.ObjectAttrs.CacheControl = "public, max-age=31536000, immutable"

	if _, err := writer.Write(content); err != nil {
		return nil, fmt.Errorf("failed to write to GCS: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close GCS writer: %w", err)
	}

	timestamp := time.Now().Unix()
	url := fmt.Sprintf("https://cdn.matiks.com/%s?timestamp=%d", objectName, timestamp)

	err = s.instituteRepo.UpdateLogo(ctx, institutionID, url)
	if err != nil {
		return nil, fmt.Errorf("failed to update institution logo URL in database: %w", err)
	}

	urlPath := s.cdnClient.ExtractPathFromURL(url)

	err = s.cdnClient.InvalidateCache(ctx, urlPath)
	if err != nil {

		zlog.Error(ctx, "Failed to invalidate CDN cache for specific institution logo URL", err,
			zap.String("path", urlPath))
	}

	return &models.File{
		Name:        file.Filename,
		Content:     string(content),
		ContentType: file.ContentType,
		URL:         url,
	}, nil
}
