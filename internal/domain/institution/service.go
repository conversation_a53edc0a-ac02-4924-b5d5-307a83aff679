package institution

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cdn"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/storage"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	instituteRepo repository.InstitutionRepository
	storage       *storage.Storage
	cdnClient     cdn.CDNClient
}

func NewInstitutionService(lc fx.Lifecycle, repo repository.InstitutionRepository, storage *storage.Storage, cdnClient cdn.CDNClient) domain.InstitutionStore {
	s := &service{
		instituteRepo: repo,
		storage:       storage,
		cdnClient:     cdnClient,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting Institution service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Institution service")
			return nil
		},
	})

	return s
}
