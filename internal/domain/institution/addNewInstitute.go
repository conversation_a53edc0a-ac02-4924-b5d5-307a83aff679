package institution

import (
	"context"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) AddNewInstitute(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error) {
	userId, err := utils.GetUserFromContext(ctx)

	if err != nil {
		return nil, err
	}

	if input.Name == "" {
		return nil, fmt.Errorf("institution name cannot be empty")
	}

	if len(input.Name) < 5 {
		return nil, fmt.Errorf("institution name must be at least 5 characters long")
	}

	if len(input.Name) > 100 {
		return nil, fmt.Errorf("institution name cannot exceed 100 characters")
	}

	institution, err := s.instituteRepo.Create(ctx, input, userId)
	if err != nil {
		return nil, err
	}

	if input.Logo != nil && input.Logo.Filename != "" {
		logoFile, err := s.UploadInstitutionLogo(ctx, *input.Logo, institution.ID)
		if err != nil || logoFile == nil {
			return nil, fmt.Errorf("failed to upload institution logo: %w", err)
		}
	}

	return institution, nil
}
