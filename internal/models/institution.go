package models

import (
	"bytes"
	"fmt"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"io"
	"strconv"

	"github.com/99designs/gqlgen/graphql"
)

type Institution struct {
	ID      primitive.ObjectID `bson:"_id" json:"id"`
	Name    string             `bson:"name" json:"name"`
	Type    InstitutionType    `bson:"type" json:"type"`
	Logo    *string            `bson:"logo" json:"logo"`
	Domains []string           `bson:"domains" json:"domains"`
	Country *string            `bson:"country" json:"country"`
	State   *string            `bson:"state" json:"state"`
	City    *string            `bson:"city" json:"city"`
	Slug    string             `bson:"slug" json:"slug"`
}

type CreateInstitutionInput struct {
	Name    string          `json:"name"`
	Type    InstitutionType `json:"type"`
	Logo    *graphql.Upload `json:"logo,omitempty"`
	Domains []string        `json:"domains"`
	Country *string         `json:"country"`
	City    *string         `json:"city"`
	State   *string         `json:"state"`
}

type InstitutionFilter struct {
	Query  string
	Limit  int64
	Offset int64
}

type InstitutionType string

const (
	InstitutionTypeSchool     InstitutionType = "SCHOOL"
	InstitutionTypeCollege    InstitutionType = "COLLEGE"
	InstitutionTypeUniversity InstitutionType = "UNIVERSITY"
	InstitutionTypeOther      InstitutionType = "OTHER"
)

var AllInstitutionType = []InstitutionType{
	InstitutionTypeSchool,
	InstitutionTypeCollege,
	InstitutionTypeUniversity,
	InstitutionTypeOther,
}

func (e InstitutionType) IsValid() bool {
	switch e {
	case InstitutionTypeSchool, InstitutionTypeCollege, InstitutionTypeUniversity, InstitutionTypeOther:
		return true
	}
	return false
}

func (e InstitutionType) String() string {
	return string(e)
}

func (e *InstitutionType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = InstitutionType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid InstitutionType", str)
	}
	return nil
}

func (e InstitutionType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *InstitutionType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e InstitutionType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
