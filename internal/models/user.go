package models

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/constants"
)

type User struct {
	ID                    ObjectID                 `json:"_id" bson:"_id"`
	Email                 *string                  `json:"email,omitempty" bson:"email,omitempty"`
	Password              *string                  `json:"-" bson:"password,omitempty"`
	Token                 *string                  `json:"token,omitempty" bson:"-"`
	Name                  *string                  `json:"name,omitempty" bson:"name,omitempty"`
	Username              string                   `json:"username" bson:"username"`
	Bio                   *string                  `json:"bio,omitempty" bson:"bio,omitempty"`
	Country               []*string                `json:"country,omitempty" bson:"country,omitempty"`
	Links                 []*string                `json:"links,omitempty" bson:"links,omitempty"`
	AwardsAndAchievements []*AwardsAndAchievements `json:"awardsAndAchievements,omitempty" bson:"awardsAndAchievements,omitempty"`
	PhoneNumber           *PhoneNumber             `json:"phoneNumber,omitempty" bson:"phoneNumber,omitempty"`
	ProfileImageURL       *string                  `json:"profileImageUrl,omitempty" bson:"profileImageUrl,omitempty"`
	Rating                *int                     `json:"rating,omitempty" bson:"rating,omitempty"`
	RatingV2              *UserRating              `json:"ratingV2" bson:"ratingV2"`
	StatikCoins           int                      `json:"statikCoins,omitempty" bson:"statikCoins,omitempty"`
	Badge                 *BadgeType               `json:"badge,omitempty" bson:"badge,omitempty"`
	CountryCode           *string                  `json:"countryCode,omitempty" bson:"countryCode,omitempty"`
	IsGuest               *bool                    `json:"isGuest,omitempty" bson:"isGuest,omitempty"`
	IsBot                 *bool                    `json:"isBot,omitempty" bson:"isBot,omitempty"`
	IsBanned              *bool                    `json:"isBanned,omitempty" bson:"isBanned,omitempty"`
	IsHumanBot            *bool                    `json:"isHumanBot,omitempty" bson:"isHumanBot,omitempty"`
	HumanBotConfig        *HumanBotConfig          `json:"humanBotConfig,omitempty" bson:"humanBotConfig,omitempty"`
	IsShadowBanned        *bool                    `json:"isShadowBanned,omitempty" bson:"isShadowBanned,omitempty"`
	ShadowBanStatus       *UserShadowBanStatus     `json:"shadowBanStatus,omitempty" bson:"shadowBanStatus,omitempty"`
	SuspiciousActivity    *bool                    `json:"suspiciousActivity,omitempty" bson:"suspiciousActivity,omitempty"`
	GlobalRank            *int                     `json:"globalRank,omitempty" bson:"globalRank,omitempty"`
	PreviousGlobalRank    *int                     `json:"previousGlobalRank,omitempty" bson:"previousGlobalRank,omitempty"`
	CountryRank           *int                     `json:"countryRank,omitempty" bson:"countryRank,omitempty"`
	PreviousCountryRank   *int                     `json:"previousCountryRank,omitempty" bson:"previousCountryRank,omitempty"`
	Stats                 *UserStats               `json:"stats,omitempty" bson:"stats,omitempty"`
	HasFixedRating        *bool                    `json:"hasFixedRating,omitempty" bson:"hasFixedRating,omitempty"`
	Additional            *UserAdditional          `json:"-" bson:"additional,omitempty"`
	UserStreaks           *UserStreaks             `json:"userStreaks,omitempty" bson:"userStreaks,omitempty"`
	League                *LeagueInfo              `json:"league,omitempty" bson:"league,omitempty"`
	CreatedAt             *time.Time               `json:"-" bson:"createdAt,omitempty"`
	UpdatedAt             *time.Time               `json:"-" bson:"updatedAt,omitempty"`
	IsSignup              bool                     `json:"isSignup,omitempty" bson:"-"`
	Timezone              *string                  `json:"timezone,omitempty" bson:"timezone,omitempty"`
	InstitutionID         *primitive.ObjectID      `json:"institutionId" bson:"institutionId,omitempty"`
	LastReadFeedID        *primitive.ObjectID      `json:"lastReadFeedId" bson:"lastReadFeedId"`
	ReferralCode          *string                  `json:"referralCode,omitempty" bson:"referralCode,omitempty"`
	IsReferred            *bool                    `json:"isReferred,omitempty" bson:"isReferred,omitempty"`
	AccountStatus         *UserAccountStatus       `json:"accountStatus,omitempty" bson:"accountStatus,omitempty"`
	IsDeleted             *bool                    `json:"isDeleted,omitempty" bson:"isDeleted,omitempty"`
}

type UserIdWithLeagueInfo struct {
	ID     primitive.ObjectID `bson:"_id"`
	League *LeagueInfo        `bson:"league"`
}

type UserLeagueStat struct {
	UserID      primitive.ObjectID   `bson:"_id"`
	StatikCoins int                  `bson:"statikCoins"`
	User        UserIdWithLeagueInfo `bson:"user"`
}

type UserRating struct {
	GlobalRating                 *int              `json:"globalRating" bson:"globalRating"`
	FlashAnzanRating             *int              `json:"flashAnzanRating" bson:"flashAnzanRating"`
	AbilityDuelsRating           *int              `json:"abilityDuelsRating" bson:"abilityDuelsRating"`
	PuzzleRating                 *int              `json:"puzzleRating" bson:"puzzleRating"`
	GlobalRatingAttributes       *RatingAttributes `json:"globalRatingAttributes" bson:"globalRatingAttributes"`
	FlashAnzanRatingAttributes   *RatingAttributes `json:"flashAnzanRatingAttributes" bson:"flashAnzanRatingAttributes"`
	AbilityDuelsRatingAttributes *RatingAttributes `json:"abilityDuelsRatingAttributes" bson:"abilityDuelsRatingAttributes"`
	PuzzleRatingAttributes       *RatingAttributes `json:"puzzleRatingAttributes" bson:"puzzleRatingAttributes"`
}

type RatingAttributes struct {
	Rating      float64 `json:"rating" bson:"rating"`
	RD          float64 `json:"rd" bson:"rd"`
	Volatility  float64 `json:"volatility" bson:"volatility"`
	LastUpdated int64   `json:"lastUpdated" bson:"lastUpdated"`
}

type AwardsAndAchievements struct {
	ImageURL    *string `json:"imageUrl,omitempty" bson:"imageUrl"`
	Link        *string `json:"link,omitempty" bson:"link"`
	Title       *string `json:"title,omitempty" bson:"title"`
	Description *string `json:"description,omitempty" bson:"description"`
}

type UserAdditional struct {
	PushNotificationTokens []PushNotificationToken `json:"-" bson:"pushNotificationTokens"`
	TimeSpent              *int64                  `json:"-" bson:"timeSpent"`
	HasUnlockedAllGames    bool                    `json:"hasUnlockedAllGames,omitempty" bson:"hasUnlockedAllGames,omitempty"`
}

type PushNotificationToken struct {
	PNT      *string `json:"-" bson:"pNT"`
	DeviceID *string `json:"-" bson:"deviceId"`
	Platform *string `json:"-" bson:"platformId"`
}

type HumanBotConfig struct {
	TargetRating         int                      `json:"targetRating" bson:"targetRating"`
	ActiveStartTime      int                      `json:"activeStartTime" bson:"activeStartTime"`
	ActiveEndTime        int                      `json:"activeEndTime" bson:"activeEndTime"`
	LastOneHourOpponents map[ObjectID][]time.Time `json:"lastOneHourOpponents" bson:"lastOneHourOpponents"`
	InGame               *bool                    `json:"InGame,omitempty" bson:"-"`
}

type UserPublicDetails struct {
	ID                  ObjectID            `json:"_id" bson:"_id"`
	Name                *string             `json:"name,omitempty" bson:"name,omitempty"`
	Username            string              `json:"username" bson:"username"`
	ProfileImageURL     *string             `json:"profileImageUrl,omitempty" bson:"profileImageUrl,omitempty"`
	Rating              *int                `json:"rating,omitempty" bson:"rating,omitempty"`
	Badge               *BadgeType          `json:"badge,omitempty" bson:"badge,omitempty"`
	CountryCode         *string             `json:"countryCode,omitempty" bson:"countryCode,omitempty"`
	IsGuest             *bool               `json:"isGuest,omitempty" bson:"isGuest,omitempty"`
	GlobalRank          *int                `json:"globalRank,omitempty" bson:"globalRank,omitempty"`
	PreviousGlobalRank  *int                `json:"previousGlobalRank,omitempty" bson:"previousGlobalRank,omitempty"`
	CountryRank         *int                `json:"countryRank,omitempty" bson:"countryRank,omitempty"`
	PreviousCountryRank *int                `json:"previousCountryRank,omitempty" bson:"previousCountryRank,omitempty"`
	Stats               *UserStats          `json:"stats,omitempty" bson:"stats,omitempty"`
	UserStreaks         *UserStreaks        `json:"userStreaks,omitempty" bson:"userStreaks,omitempty"`
	RatingV2            *UserRating         `json:"ratingV2" bson:"ratingV2"`
	InstitutionID       *primitive.ObjectID `json:"institutionId" bson:"institutionId,omitempty"`
}

type UserRatingCount struct {
	Rating int   `json:"_id" bson:"_id"`
	Count  int64 `json:"count" bson:"count"`
}

type UserGame struct {
	ID         *string    `json:"id,omitempty" bson:"id,omitempty"`
	OpponentID *ObjectID  `json:"opponentId,omitempty" bson:"opponentId,omitempty"`
	IsWinner   *bool      `json:"isWinner,omitempty" bson:"isWinner,omitempty"`
	ST         *time.Time `json:"sT,omitempty" bson:"sT,omitempty"`
}

type UserStats struct {
	Ngp             int         `json:"ngp,omitempty" bson:"ngp"`
	Hr              int         `json:"hr,omitempty" bson:"hr"`
	FollowersCount  int         `json:"followersCount,omitempty" bson:"followersCount"`
	FollowingsCount int         `json:"followingsCount,omitempty" bson:"followingsCount"`
	FriendsCount    int         `json:"friendsCount,omitempty" bson:"friendsCount"`
	Last10BotGames  []*UserGame `json:"last10BotGames,omitempty" bson:"last10BotGames"`
	Games           []*UserGame `json:"games,omitempty" bson:"games"`
}

type PhoneNumber struct {
	CountryCode *string `json:"countryCode,omitempty" bson:"countryCode,omitempty"`
	Number      *string `json:"number,omitempty" bson:"number,omitempty"`
}

type LeaderboardConnection struct {
	Edges    []*LeaderboardEdge `json:"edges,omitempty" bson:"edges,omitempty"`
	PageInfo *PageInfo          `json:"pageInfo" bson:"pageInfo"`
}

type UserLeaderboardPage struct {
	Edges      []*LeaderboardEdge `json:"edges,omitempty" bson:"edges,omitempty"`
	TotalCount int                `json:"totalCount" bson:"totalCount"`
}

type LeaderboardParams struct {
	AfterID  primitive.ObjectID
	Page     int
	PageSize int
}

type LeaderboardEdge struct {
	Cursor string             `json:"cursor" bson:"cursor"`
	Node   *UserPublicDetails `json:"node" bson:"node"`
}

type PageInfo struct {
	HasNextPage bool    `json:"hasNextPage" bson:"hasNextPage"`
	EndCursor   *string `json:"endCursor,omitempty" bson:"endCursor,omitempty"`
}

type UpdateUserInput struct {
	Name                  *string                       `json:"name,omitempty" bson:"name"`
	ProfileImageURL       *string                       `json:"profileImageUrl,omitempty" bson:"profileImageUrl"`
	CountryCode           *string                       `json:"countryCode,omitempty" bson:"countryCode"`
	Timezone              *string                       `json:"timezone,omitempty" bson:"timezone"`
	Username              *string                       `json:"username,omitempty" bson:"username"`
	Bio                   *string                       `json:"bio,omitempty" bson:"bio,omitempty"`
	Country               []*string                     `json:"country,omitempty" bson:"country,omitempty"`
	Links                 []*string                     `json:"links,omitempty" bson:"links,omitempty"`
	AwardsAndAchievements []*AwardsAndAchievementsInput `json:"awardsAndAchievements,omitempty" bson:"awardsAndAchievements,omitempty"`
	InstitutionID         *ObjectID                     `json:"institutionId,omitempty" bson:"institutionId,omitempty"` // Add institution ID field
}

type UserDailyActivity struct {
	ID                primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	UserID            primitive.ObjectID `bson:"userId" json:"userId"`
	Date              time.Time          `bson:"date" json:"date"`
	DateString        string             `bson:"dateString" json:"dateString"`
	TotalDuration     int64              `bson:"totalDuration" json:"totalDuration"`
	StatikCoinsEarned int                `bson:"statikCoinsEarned" json:"statikCoinsEarned"`
	ActivityBreakdown map[string]int     `bson:"activityBreakdown" json:"activityBreakdown"`
}

// UserActivity is kept for backward compatibility during migration
type UserActivityTemp UserDailyActivity

type AppleSignInFullNameInput struct {
	FamilyName *string `json:"familyName,omitempty" bson:"familyName"`
	GivenName  *string `json:"givenName,omitempty" bson:"givenName"`
	MiddleName *string `json:"middleName,omitempty" bson:"middleName"`
	NamePrefix *string `json:"namePrefix,omitempty" bson:"namePrefix"`
	NameSuffix *string `json:"nameSuffix,omitempty" bson:"nameSuffix"`
	Nickname   *string `json:"nickname,omitempty" bson:"nickname"`
}

type AppleSignInInput struct {
	AuthorizationCode string                    `json:"authorizationCode" bson:"authorizationCode"`
	Email             *string                   `json:"email,omitempty" bson:"email"`
	FullName          *AppleSignInFullNameInput `json:"fullName,omitempty" bson:"fullName"`
	IdentityToken     string                    `json:"identityToken" bson:"identityToken"`
	RealUserStatus    *int                      `json:"realUserStatus,omitempty" bson:"realUserStatus"`
	State             *string                   `json:"state,omitempty" bson:"state"`
	User              *string                   `json:"user,omitempty" bson:"user"`
}

type AwardsAndAchievementsInput struct {
	ImageURL    *string `json:"imageUrl,omitempty" bson:"imageUrl"`
	Link        *string `json:"link,omitempty" bson:"link"`
	Title       *string `json:"title,omitempty" bson:"title"`
	Description *string `json:"description,omitempty" bson:"description"`
}

type UserInput struct {
	Email    string `json:"email" bson:"email"`
	Name     string `json:"name,omitempty" bson:"name,omitempty"`
	Password string `json:"password" bson:"password"`
	Confirm  string `json:"confirm" bson:"confirm"`
}

type DeviceTokenRegistrationResponse struct {
	Success bool   `json:"success" bson:"success"`
	Message string `json:"message" bson:"message"`
}

type RematchRequestOutput struct {
	GameID      *string `json:"gameId,omitempty" bson:"gameId"`
	RequestedBy *string `json:"requestedBy,omitempty" bson:"requestedBy"`
	Status      *string `json:"status,omitempty" bson:"status"`
	NewGameID   *string `json:"newGameId,omitempty" bson:"newGameId"`
	User        *User   `json:"user,omitempty" bson:"user"`
	WaitingTime *int    `json:"waitingTime,omitempty" bson:"waitingTime"`
	GameType    *string `json:"gameType,omitempty" bson:"gameType"`
}

type BadgeAssignedEvent struct {
	InitialBadge *BadgeType `json:"initialBadge,omitempty" bson:"initialBadge"`
	NewBadge     *BadgeType `json:"newBadge,omitempty" bson:"newBadge"`
}

type StatikCoinsEarnedEvent struct {
	StatikCoinsEarned *int    `json:"statikCoinsEarned" bson:"statikCoinsEarned"`
	ActivityType      *string `json:"activityType" bson:"activityType"`
}

type GroupedHumanBots struct {
	TargetRating int     `json:"targetRating" bson:"targetRating"`
	HumanBots    []*User `json:"humanBots" bson:"humanBots"`
}

type ShowdownFicturesCreatedEvent struct {
	Showdown       *ShowdownMinifiedNotificationPayload `json:"showdown" bson:"showdown"`
	CurrentFixture *Fictures                            `json:"currentFictures" bson:"currentFictures"`
	Participant    *CurrentShowdonParticipant           `json:"participant" bson:"participant"`
}

type ShowdownParticipantUpdatedEvent struct {
	Participant *CurrentShowdonParticipant `json:"participant" bson:"participant"`
}

type ShowdownToStartEvent struct {
	ShowdownID *ObjectID                            `json:"showdownId" bson:"showdownId"`
	Showdown   *ShowdownMinifiedNotificationPayload `json:"showdown" bson:"showdown"`
}

type GameCanceledOutput struct {
	GameID     *string          `json:"gameId,omitempty" bson:"gameId"`
	CreatorID  *string          `json:"creatorId,omitempty" bson:"creatorId"`
	OpponentID *string          `json:"opponentId,omitempty" bson:"opponentId"`
	Status     *ChallengeStatus `json:"status,omitempty" bson:"status"`
}

type InAppNotificationPayload struct {
	InAppNotification
}

type StreakMaintained struct {
	IsPlayedToday bool `json:"isPlayedToday" bson:"isPlayedToday"`
}

type JoinedWeeklyLeagueEvent struct {
	LeagueInfo LeagueInfo `json:"leagueInfo,omitempty" bson:"leagueInfo,omitempty"`
}

type RatingFixtureOutput struct {
	NewRating *int `json:"newRating,omitempty" bson:"newRating"`
}

type SearchTimeoutEvent struct {
	Event string `json:"event" bson:"event"`
}

type StatikCoinLeaderboardEntry struct {
	User        *UserPublicDetails `json:"user,omitempty" bson:"user"`
	StatikCoins *int               `json:"statikCoins,omitempty" bson:"statikCoins"`
	Rank        *int               `json:"rank,omitempty" bson:"rank"`
}

type WeeklyLeagueLeaderboardEntry struct {
	User          *UserPublicDetails         `json:"user,omitempty" bson:"user"`
	StatikCoins   *int                       `json:"statikCoins,omitempty" bson:"statikCoins"`
	Rank          *int                       `json:"rank,omitempty" bson:"rank"`
	ProgressState *WeeklyLeagueProgressState `json:"progressState,omitempty" bson:"progressState"`
}

type WeeklyLeagueLeaderboardPage struct {
	Results           []*WeeklyLeagueLeaderboardEntry `json:"results" bson:"results"`
	CurrentUserLeague *LeagueInfo                     `json:"currentUserLeague,omitempty" bson:"currentUserLeague"`
	PageNumber        int                             `json:"pageNumber" bson:"pageNumber"`
	PageSize          int                             `json:"pageSize" bson:"pageSize"`
	HasMore           *bool                           `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults      *int                            `json:"totalResults,omitempty" bson:"totalResults"`
}

type StatikCoinLeaderboardPage struct {
	Results      []*StatikCoinLeaderboardEntry `json:"results" bson:"results"`
	PageNumber   int                           `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                           `json:"pageSize" bson:"pageSize"`
	HasMore      *bool                         `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                          `json:"totalResults,omitempty" bson:"totalResults"`
}

type MyInstituteUsersPage struct {
	Results      []*SearchUserOutput `json:"results" bson:"results"`
	PageNumber   int64               `json:"pageNumber" bson:"pageNumber"`
	PageSize     int64               `json:"pageSize" bson:"pageSize"`
	HasMore      bool                `json:"hasMore" bson:"hasMore"`
	TotalResults int64               `json:"totalResults" bson:"totalResults"`
}

type PublishMessagePayload struct {
	Message Message `json:"message" bson:"message"`
}

type StatikCoinLeaderboardType string

const (
	StatikCoinLeaderboardTypeDaily   StatikCoinLeaderboardType = "DAILY"
	StatikCoinLeaderboardTypeMonthly StatikCoinLeaderboardType = "MONTHLY"
	StatikCoinLeaderboardTypeWeekly  StatikCoinLeaderboardType = "WEEKLY"
	StatikCoinLeaderboardTypeAllTime StatikCoinLeaderboardType = "ALL_TIME"
)

var AllStatikCoinLeaderboardType = []StatikCoinLeaderboardType{
	StatikCoinLeaderboardTypeDaily,
	StatikCoinLeaderboardTypeMonthly,
	StatikCoinLeaderboardTypeWeekly,
	StatikCoinLeaderboardTypeAllTime,
}

func (e StatikCoinLeaderboardType) IsValid() bool {
	switch e {
	case StatikCoinLeaderboardTypeDaily, StatikCoinLeaderboardTypeMonthly, StatikCoinLeaderboardTypeWeekly, StatikCoinLeaderboardTypeAllTime:
		return true
	}
	return false
}

func (e StatikCoinLeaderboardType) String() string {
	return string(e)
}

func (e *StatikCoinLeaderboardType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = StatikCoinLeaderboardType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid StatikCoinLeaderboardType", str)
	}
	return nil
}

func (e StatikCoinLeaderboardType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type UserEvent interface {
	IsUserEvent()
	GetEventType() constants.UserEvent
}

func (g ChallengeForPuzzleGameOutput) IsUserEvent() {}
func (g ChallengeForPuzzleGameOutput) GetEventType() constants.UserEvent {
	return constants.ChallengeForPuzzleGameOutput
}

func (g PuzzleGameEventWithOpponentOutput) IsUserEvent() {}
func (g PuzzleGameEventWithOpponentOutput) GetEventType() constants.UserEvent {
	return constants.PuzzleGameEventWithOpponentOutput
}

func (g GameCanceledOutput) IsUserEvent() {}
func (g GameCanceledOutput) GetEventType() constants.UserEvent {
	return constants.GameCanceledOutput
}

func (g StreakMaintained) IsUserEvent() {}
func (g StreakMaintained) GetEventType() constants.UserEvent {
	return constants.StreakMaintainedEvent
}

func (RatingFixtureOutput) IsUserEvent() {}
func (RatingFixtureOutput) GetEventType() constants.UserEvent {
	return constants.RatingFixtureOutput
}

func (JoinedWeeklyLeagueEvent) IsUserEvent() {}
func (JoinedWeeklyLeagueEvent) GetEventType() constants.UserEvent {
	return constants.JoinedWeeklyLeagueEvent
}

func (g StatikCoinsEarnedEvent) IsUserEvent() {
}

func (g StatikCoinsEarnedEvent) GetEventType() constants.UserEvent {
	return constants.StatikCoinsEarnedEvent
}

func (r *RematchRequestOutput) IsUserEvent() {}
func (r *RematchRequestOutput) GetEventType() constants.UserEvent {
	return constants.UserEventRematchRequested
}

func (b *BadgeAssignedEvent) IsUserEvent() {}
func (b *BadgeAssignedEvent) GetEventType() constants.UserEvent {
	return constants.UserEventBadgeAssigned
}

func (s *SearchSubscriptionOutput) IsUserEvent() {}
func (s *SearchSubscriptionOutput) GetEventType() constants.UserEvent {
	return constants.UserEventUserMatched
}

func (c *ChallengeOutput) IsUserEvent() {}
func (c *ChallengeOutput) GetEventType() constants.UserEvent {
	return constants.ChallengeUserEnumStruct
}

func (s *ShowdownFicturesCreatedEvent) IsUserEvent() {}
func (s *ShowdownFicturesCreatedEvent) GetEventType() constants.UserEvent {
	return constants.UserEventShowdownFicturesCreated
}

func (s *ShowdownToStartEvent) IsUserEvent() {}
func (s *ShowdownToStartEvent) GetEventType() constants.UserEvent {
	return constants.UserEventShowdownToStart
}

func (s *ShowdownParticipantUpdatedEvent) IsUserEvent() {}
func (s *ShowdownParticipantUpdatedEvent) GetEventType() constants.UserEvent {
	return constants.UserEventShowdownParticipantUpdated
}

func (s *PublishMessagePayload) IsUserEvent() {}
func (s *PublishMessagePayload) GetEventType() constants.UserEvent {
	return constants.MessagePublishedEvent
}

func (s *SearchTimeoutEvent) IsUserEvent() {}
func (s *SearchTimeoutEvent) GetEventType() constants.UserEvent {
	return constants.SearchTimeoutEvent
}

func (s *InAppNotificationPayload) IsUserEvent() {}
func (s *InAppNotificationPayload) GetEventType() constants.UserEvent {
	return constants.UserEventTypeInAppNotification
}

type BadgeType string

const (
	BadgeTypeRookie               BadgeType = "ROOKIE"
	BadgeTypeNovice               BadgeType = "NOVICE"
	BadgeTypeAmateur              BadgeType = "AMATEUR"
	BadgeTypeExpert               BadgeType = "EXPERT"
	BadgeTypeCandidateMaster      BadgeType = "CANDIDATE_MASTER"
	BadgeTypeMaster               BadgeType = "MASTER"
	BadgeTypeGrandmaster          BadgeType = "GRANDMASTER"
	BadgeTypeLegendaryGrandmaster BadgeType = "LEGENDARY_GRANDMASTER"
	BadgeTypeGoat                 BadgeType = "GOAT"
)

var AllBadgeType = []BadgeType{
	BadgeTypeRookie,
	BadgeTypeNovice,
	BadgeTypeAmateur,
	BadgeTypeExpert,
	BadgeTypeCandidateMaster,
	BadgeTypeMaster,
	BadgeTypeGrandmaster,
	BadgeTypeLegendaryGrandmaster,
	BadgeTypeGoat,
}

func (e BadgeType) IsValid() bool {
	switch e {
	case BadgeTypeRookie, BadgeTypeNovice, BadgeTypeAmateur, BadgeTypeExpert, BadgeTypeCandidateMaster, BadgeTypeMaster, BadgeTypeGrandmaster, BadgeTypeLegendaryGrandmaster, BadgeTypeGoat:
		return true
	}
	return false
}

func (e BadgeType) String() string {
	return string(e)
}

func (e *BadgeType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = BadgeType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid BadgeType", str)
	}
	return nil
}

func (e BadgeType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type SearchUserOutput struct {
	UserPublicDetails *User             `json:"userPublicDetails,omitempty" bson:"userPublicDetails"`
	IsFollowing       *bool             `json:"isFollowing,omitempty" bson:"isFollowing"`
	FriendshipStatus  *FriendshipStatus `json:"friendshipStatus,omitempty" bson:"friendshipStatus"`
}

type SearchUserPage struct {
	Results      []*SearchUserOutput `json:"results" bson:"results"`
	PageNumber   int                 `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                 `json:"pageSize" bson:"pageSize"`
	HasMore      *bool               `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                `json:"totalResults,omitempty" bson:"totalResults"`
}

type OnlineUsersPage struct {
	Users        []*UserDetailWithActivity `json:"users" bson:"users"`
	PageNumber   int                       `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                       `json:"pageSize" bson:"pageSize"`
	HasMore      *bool                     `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                      `json:"totalResults,omitempty" bson:"totalResults"`
}

type UserDetailWithActivity struct {
	UserInfo     *UserPublicDetails `json:"userInfo" bson:"userInfo"`
	CurrActivity *UserActivityType  `json:"currActivity,omitempty" bson:"currActivity"`
}

type TopPlayerEntry struct {
	User   *UserPublicDetails `json:"user" bson:"user"`
	Rating int                `json:"rating" bson:"rating"`
	Rank   int                `json:"rank" bson:"rank"`
}

type TopPlayersLeaderboard struct {
	GlobalRating  []*TopPlayerEntry `json:"globalRating"`
	MemoryRating  []*TopPlayerEntry `json:"memoryRating"`
	AbilityRating []*TopPlayerEntry `json:"abilityRating"`
}

type UserOnlineStatus string

const (
	UserOnlineStatusOnline  UserOnlineStatus = "ONLINE"
	UserOnlineStatusOffline UserOnlineStatus = "OFFLINE"
)

var AllUserOnlineStatus = []UserOnlineStatus{
	UserOnlineStatusOnline,
	UserOnlineStatusOffline,
}

func (e UserOnlineStatus) IsValid() bool {
	switch e {
	case UserOnlineStatusOnline, UserOnlineStatusOffline:
		return true
	}
	return false
}

func (e UserOnlineStatus) String() string {
	return string(e)
}

func (e *UserOnlineStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = UserOnlineStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid UserOnlineStatus", str)
	}
	return nil
}

func (e UserOnlineStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type LeagueType string

const (
	LeagueTypeMatikan LeagueType = "MATIKAN"
	LeagueTypeRuby    LeagueType = "RUBY"
	LeagueTypeDiamond LeagueType = "DIAMOND"
	LeagueTypeGold    LeagueType = "GOLD"
	LeagueTypeSilver  LeagueType = "SILVER"
	LeagueTypeBronze  LeagueType = "BRONZE"
)

var AllLeagueType = []LeagueType{
	LeagueTypeDiamond,
	LeagueTypeMatikan,
	LeagueTypeGold,
	LeagueTypeSilver,
	LeagueTypeBronze,
	LeagueTypeRuby,
}

func (e LeagueType) IsValid() bool {
	switch e {
	case LeagueTypeDiamond, LeagueTypeRuby, LeagueTypeMatikan, LeagueTypeGold, LeagueTypeSilver, LeagueTypeBronze:
		return true
	}
	return false
}

func (e LeagueType) String() string {
	return string(e)
}

func (e *LeagueType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = LeagueType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid LeagueType", str)
	}
	return nil
}

func (e LeagueType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type UserActivityType string

const (
	UserInLobby          UserActivityType = "USER_IN_LOBBY"
	InGame               UserActivityType = "IN_GAME"
	SearchingForOpponent UserActivityType = "SEARCHING_FOR_OPPONENT"
	PlayingDc            UserActivityType = "PLAYING_DC"
	InDcWaitingRoom      UserActivityType = "IN_DC_WAITING_ROOM"
	ViewingDcLeaderboard UserActivityType = "VIEWING_DC_LEADERBOARD"
	Exploring            UserActivityType = "EXPLORING"
	PracticingNets       UserActivityType = "PRACTICING_NETS"
	SelectingConfig      UserActivityType = "SELECTING_CONFIG"
	FixingRating         UserActivityType = "FIXING_RATING"
	Onboarding           UserActivityType = "ONBOARDING"
	ViewingGameResult    UserActivityType = "VIEWING_GAME_RESULT"
	OnChatPage           UserActivityType = "ON_CHAT_PAGE"
	InContest            UserActivityType = "IN_CONTEST"
)

func (e UserActivityType) IsValid() bool {
	switch e {
	case UserInLobby, InGame, SearchingForOpponent, PlayingDc, InDcWaitingRoom, ViewingDcLeaderboard, Exploring, PracticingNets, SelectingConfig, FixingRating, Onboarding, ViewingGameResult, OnChatPage, InContest:
		return true
	}
	return false
}

var AllUserActivityEnum = []UserActivityType{
	UserInLobby,
	InGame,
	SearchingForOpponent,
	PlayingDc,
	InDcWaitingRoom,
	ViewingDcLeaderboard,
	Exploring,
	PracticingNets,
	SelectingConfig,
	FixingRating,
	Onboarding,
	ViewingGameResult,
	OnChatPage,
	InContest,
}

func (e UserActivityType) String() string {
	return string(e)
}

func (e *UserActivityType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = UserActivityType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid UserActivityEnum", str)
	}
	return nil
}

func (e UserActivityType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type LeagueInfo struct {
	League            *LeagueType                `json:"league,omitempty" bson:"league"`
	GroupID           *int                       `json:"groupId,omitempty" bson:"groupId"`
	UpdatedAt         *time.Time                 `json:"updatedAt,omitempty" bson:"updatedAt"`
	HasParticipated   *bool                      `json:"hasParticipated,omitempty" bson:"hasParticipated"`
	CoinsTillLastWeek *int                       `json:"coinsTillLastWeek,omitempty" bson:"coinsTillLastWeek"`
	ProgressState     *WeeklyLeagueProgressState `json:"progressState,omitempty" bson:"progressState"`
}

type WeeklyLeagueProgressState string

const (
	WeeklyLeagueProgressStatePromotion WeeklyLeagueProgressState = "PROMOTION"
	WeeklyLeagueProgressStateDemotion  WeeklyLeagueProgressState = "DEMOTION"
	WeeklyLeagueProgressStateNoChange  WeeklyLeagueProgressState = "NO_CHANGE"
)

var AllWeeklyLeagueProgressState = []WeeklyLeagueProgressState{
	WeeklyLeagueProgressStatePromotion,
	WeeklyLeagueProgressStateDemotion,
	WeeklyLeagueProgressStateNoChange,
}

func (e WeeklyLeagueProgressState) IsValid() bool {
	switch e {
	case WeeklyLeagueProgressStatePromotion, WeeklyLeagueProgressStateDemotion, WeeklyLeagueProgressStateNoChange:
		return true
	}
	return false
}

func (e WeeklyLeagueProgressState) String() string {
	return string(e)
}

func (e *WeeklyLeagueProgressState) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = WeeklyLeagueProgressState(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid WeeklyLeagueProgressState", str)
	}
	return nil
}

type UsersWeeklyStatikCoinsOutput struct {
	TotalCoins int   `json:"totalCoins" bson:"totalCoins"`
	DailyCoins []int `json:"dailyCoins" bson:"dailyCoins"`
}

func (e WeeklyLeagueProgressState) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type UserAccountStatus string

const (
	UserAccountStatusActive    UserAccountStatus = "ACTIVE"
	UserAccountStatusInactive  UserAccountStatus = "INACTIVE"
	UserAccountStatusBanned    UserAccountStatus = "BANNED"
	UserAccountStatusDeleted   UserAccountStatus = "DELETED"
	UserAccountStatusSuspended UserAccountStatus = "SUSPENDED"
)

var AllUserAccountStatus = []UserAccountStatus{
	UserAccountStatusActive,
	UserAccountStatusInactive,
	UserAccountStatusBanned,
	UserAccountStatusDeleted,
	UserAccountStatusSuspended,
}

func (e UserAccountStatus) IsValid() bool {
	switch e {
	case UserAccountStatusActive, UserAccountStatusInactive, UserAccountStatusBanned, UserAccountStatusDeleted, UserAccountStatusSuspended:
		return true
	}
	return false
}

func (e UserAccountStatus) String() string {
	return string(e)
}

func (e *UserAccountStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = UserAccountStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid UserAccountStatus", str)
	}
	return nil
}

func (e UserAccountStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *UserAccountStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e UserAccountStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
