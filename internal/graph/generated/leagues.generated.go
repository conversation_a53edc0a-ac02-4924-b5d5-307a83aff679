// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _ActivitySummaryEntry_activity(ctx context.Context, field graphql.CollectedField, obj *models.ActivitySummaryEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ActivitySummaryEntry_activity(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Activity, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ActivitySummaryEntry_activity(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ActivitySummaryEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ActivitySummaryEntry_coins(ctx context.Context, field graphql.CollectedField, obj *models.ActivitySummaryEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ActivitySummaryEntry_coins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Coins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ActivitySummaryEntry_coins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ActivitySummaryEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_id(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_name(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_details(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_details(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Details, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.LeagueDetails)
	fc.Result = res
	return ec.marshalOLeagueDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_details(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "about":
				return ec.fieldContext_LeagueDetails_about(ctx, field)
			case "requirements":
				return ec.fieldContext_LeagueDetails_requirements(ctx, field)
			case "instructions":
				return ec.fieldContext_LeagueDetails_instructions(ctx, field)
			case "awards":
				return ec.fieldContext_LeagueDetails_awards(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_hostedBy(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_hostedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_hostedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_hostedByV2(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_hostedByV2(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostedByV2, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.HostDetails)
	fc.Result = res
	return ec.marshalOHostDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_hostedByV2(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_HostDetails_name(ctx, field)
			case "logo":
				return ec.fieldContext_HostDetails_logo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type HostDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_registrationStart(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_registrationStart(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationStart, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_registrationStart(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_registrationEnd(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_registrationEnd(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationEnd, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_registrationEnd(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_leagueStart(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_leagueStart(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeagueStart, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_leagueStart(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_leagueEnd(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_leagueEnd(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeagueEnd, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_leagueEnd(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_registrationCount(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_registrationCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int64)
	fc.Result = res
	return ec.marshalOInt2ᚖint64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_registrationCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_chatRoomId(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_chatRoomId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChatRoomId, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_chatRoomId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_currentUserParticipation(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_currentUserParticipation(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserParticipation, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.LeagueParticipant)
	fc.Result = res
	return ec.marshalOLeagueParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueParticipant(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_currentUserParticipation(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_LeagueParticipant_id(ctx, field)
			case "userId":
				return ec.fieldContext_LeagueParticipant_userId(ctx, field)
			case "leagueId":
				return ec.fieldContext_LeagueParticipant_leagueId(ctx, field)
			case "joinedAt":
				return ec.fieldContext_LeagueParticipant_joinedAt(ctx, field)
			case "registrationData":
				return ec.fieldContext_LeagueParticipant_registrationData(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueParticipant", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_currentUserResult(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_currentUserResult(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserResult, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.LeagueLeaderboardEntry)
	fc.Result = res
	return ec.marshalOLeagueLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_currentUserResult(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_LeagueLeaderboardEntry_user(ctx, field)
			case "statikCoins":
				return ec.fieldContext_LeagueLeaderboardEntry_statikCoins(ctx, field)
			case "rank":
				return ec.fieldContext_LeagueLeaderboardEntry_rank(ctx, field)
			case "activitySummary":
				return ec.fieldContext_LeagueLeaderboardEntry_activitySummary(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueLeaderboardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _League_registrationForm(ctx context.Context, field graphql.CollectedField, obj *models.League) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_League_registrationForm(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationForm, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.RegistrationForm)
	fc.Result = res
	return ec.marshalORegistrationForm2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationForm(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_League_registrationForm(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "League",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_RegistrationForm__id(ctx, field)
			case "fields":
				return ec.fieldContext_RegistrationForm_fields(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationForm", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueDetails_about(ctx context.Context, field graphql.CollectedField, obj *models.LeagueDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueDetails_about(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.About, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueDetails_about(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueDetails_requirements(ctx context.Context, field graphql.CollectedField, obj *models.LeagueDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueDetails_requirements(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Requirements, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueDetails_requirements(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueDetails_instructions(ctx context.Context, field graphql.CollectedField, obj *models.LeagueDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueDetails_instructions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Instructions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueDetails_instructions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueDetails_awards(ctx context.Context, field graphql.CollectedField, obj *models.LeagueDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueDetails_awards(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Awards, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueDetails_awards(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueLeaderboardEntry_user(ctx context.Context, field graphql.CollectedField, obj *models.LeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueLeaderboardEntry_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.User)
	fc.Result = res
	return ec.marshalNUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueLeaderboardEntry_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_User__id(ctx, field)
			case "email":
				return ec.fieldContext_User_email(ctx, field)
			case "token":
				return ec.fieldContext_User_token(ctx, field)
			case "name":
				return ec.fieldContext_User_name(ctx, field)
			case "username":
				return ec.fieldContext_User_username(ctx, field)
			case "bio":
				return ec.fieldContext_User_bio(ctx, field)
			case "country":
				return ec.fieldContext_User_country(ctx, field)
			case "links":
				return ec.fieldContext_User_links(ctx, field)
			case "awardsAndAchievements":
				return ec.fieldContext_User_awardsAndAchievements(ctx, field)
			case "phoneNumber":
				return ec.fieldContext_User_phoneNumber(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_User_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_User_rating(ctx, field)
			case "ratingV2":
				return ec.fieldContext_User_ratingV2(ctx, field)
			case "statikCoins":
				return ec.fieldContext_User_statikCoins(ctx, field)
			case "badge":
				return ec.fieldContext_User_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_User_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_User_isGuest(ctx, field)
			case "isBot":
				return ec.fieldContext_User_isBot(ctx, field)
			case "isShadowBanned":
				return ec.fieldContext_User_isShadowBanned(ctx, field)
			case "shadowBanStatus":
				return ec.fieldContext_User_shadowBanStatus(ctx, field)
			case "suspiciousActivity":
				return ec.fieldContext_User_suspiciousActivity(ctx, field)
			case "globalRank":
				return ec.fieldContext_User_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_User_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_User_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_User_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_User_stats(ctx, field)
			case "hasFixedRating":
				return ec.fieldContext_User_hasFixedRating(ctx, field)
			case "isSignup":
				return ec.fieldContext_User_isSignup(ctx, field)
			case "userStreaks":
				return ec.fieldContext_User_userStreaks(ctx, field)
			case "timezone":
				return ec.fieldContext_User_timezone(ctx, field)
			case "additional":
				return ec.fieldContext_User_additional(ctx, field)
			case "league":
				return ec.fieldContext_User_league(ctx, field)
			case "institutionId":
				return ec.fieldContext_User_institutionId(ctx, field)
			case "institutionName":
				return ec.fieldContext_User_institutionName(ctx, field)
			case "lastReadFeedId":
				return ec.fieldContext_User_lastReadFeedId(ctx, field)
			case "referralCode":
				return ec.fieldContext_User_referralCode(ctx, field)
			case "isReferred":
				return ec.fieldContext_User_isReferred(ctx, field)
			case "isDeleted":
				return ec.fieldContext_User_isDeleted(ctx, field)
			case "accountStatus":
				return ec.fieldContext_User_accountStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type User", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueLeaderboardEntry_statikCoins(ctx context.Context, field graphql.CollectedField, obj *models.LeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueLeaderboardEntry_statikCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueLeaderboardEntry_statikCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueLeaderboardEntry_rank(ctx context.Context, field graphql.CollectedField, obj *models.LeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueLeaderboardEntry_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueLeaderboardEntry_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueLeaderboardEntry_activitySummary(ctx context.Context, field graphql.CollectedField, obj *models.LeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueLeaderboardEntry_activitySummary(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ActivitySummary, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ActivitySummaryEntry)
	fc.Result = res
	return ec.marshalNActivitySummaryEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐActivitySummaryEntryᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueLeaderboardEntry_activitySummary(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "activity":
				return ec.fieldContext_ActivitySummaryEntry_activity(ctx, field)
			case "coins":
				return ec.fieldContext_ActivitySummaryEntry_coins(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ActivitySummaryEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueLeaderboardPage_participants(ctx context.Context, field graphql.CollectedField, obj *models.LeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueLeaderboardPage_participants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Participants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.LeagueLeaderboardEntry)
	fc.Result = res
	return ec.marshalNLeagueLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardEntryᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueLeaderboardPage_participants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_LeagueLeaderboardEntry_user(ctx, field)
			case "statikCoins":
				return ec.fieldContext_LeagueLeaderboardEntry_statikCoins(ctx, field)
			case "rank":
				return ec.fieldContext_LeagueLeaderboardEntry_rank(ctx, field)
			case "activitySummary":
				return ec.fieldContext_LeagueLeaderboardEntry_activitySummary(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueLeaderboardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueLeaderboardPage_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.LeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueLeaderboardPage_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueLeaderboardPage_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueParticipant_id(ctx context.Context, field graphql.CollectedField, obj *models.LeagueParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueParticipant_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueParticipant_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueParticipant_userId(ctx context.Context, field graphql.CollectedField, obj *models.LeagueParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueParticipant_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueParticipant_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueParticipant_leagueId(ctx context.Context, field graphql.CollectedField, obj *models.LeagueParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueParticipant_leagueId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeagueID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueParticipant_leagueId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueParticipant_joinedAt(ctx context.Context, field graphql.CollectedField, obj *models.LeagueParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueParticipant_joinedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.JoinedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueParticipant_joinedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueParticipant_registrationData(ctx context.Context, field graphql.CollectedField, obj *models.LeagueParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueParticipant_registrationData(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationData, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.RegistrationFieldData)
	fc.Result = res
	return ec.marshalORegistrationFieldData2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueParticipant_registrationData(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_RegistrationFieldData_name(ctx, field)
			case "values":
				return ec.fieldContext_RegistrationFieldData_values(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationFieldData", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedLeagues_league(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedLeagues) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedLeagues_league(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.League, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.League)
	fc.Result = res
	return ec.marshalNLeague2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedLeagues_league(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedLeagues",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_League_id(ctx, field)
			case "name":
				return ec.fieldContext_League_name(ctx, field)
			case "details":
				return ec.fieldContext_League_details(ctx, field)
			case "hostedBy":
				return ec.fieldContext_League_hostedBy(ctx, field)
			case "hostedByV2":
				return ec.fieldContext_League_hostedByV2(ctx, field)
			case "registrationStart":
				return ec.fieldContext_League_registrationStart(ctx, field)
			case "registrationEnd":
				return ec.fieldContext_League_registrationEnd(ctx, field)
			case "leagueStart":
				return ec.fieldContext_League_leagueStart(ctx, field)
			case "leagueEnd":
				return ec.fieldContext_League_leagueEnd(ctx, field)
			case "registrationCount":
				return ec.fieldContext_League_registrationCount(ctx, field)
			case "chatRoomId":
				return ec.fieldContext_League_chatRoomId(ctx, field)
			case "currentUserParticipation":
				return ec.fieldContext_League_currentUserParticipation(ctx, field)
			case "currentUserResult":
				return ec.fieldContext_League_currentUserResult(ctx, field)
			case "registrationForm":
				return ec.fieldContext_League_registrationForm(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type League", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedLeagues_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedLeagues) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedLeagues_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedLeagues_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedLeagues",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputCreateLeagueInput(ctx context.Context, obj any) (models.CreateLeagueInput, error) {
	var it models.CreateLeagueInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"name", "hostedBy", "registrationStart", "registrationEnd", "leagueStart", "leagueEnd", "registrationFormInput", "details", "hostLogo"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "hostedBy":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostedBy"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostedBy = data
		case "registrationStart":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationStart"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationStart = data
		case "registrationEnd":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationEnd"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationEnd = data
		case "leagueStart":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("leagueStart"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.LeagueStart = data
		case "leagueEnd":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("leagueEnd"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.LeagueEnd = data
		case "registrationFormInput":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationFormInput"))
			data, err := ec.unmarshalORegistrationFormInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationFormInput = data
		case "details":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("details"))
			data, err := ec.unmarshalOLeagueDetailsInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueDetailsInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.Details = data
		case "hostLogo":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostLogo"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostLogo = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputJoinLeagueInput(ctx context.Context, obj any) (models.JoinLeagueInput, error) {
	var it models.JoinLeagueInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"leagueId", "formData"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "leagueId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("leagueId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.LeagueID = data
		case "formData":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("formData"))
			data, err := ec.unmarshalNRegistrationFormFieldValueInput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormFieldValueInputᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.FormData = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputLeagueDetailsInput(ctx context.Context, obj any) (models.LeagueDetailsInput, error) {
	var it models.LeagueDetailsInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"about", "requirements", "instructions", "awards"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "about":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("about"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.About = data
		case "requirements":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("requirements"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Requirements = data
		case "instructions":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("instructions"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Instructions = data
		case "awards":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("awards"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Awards = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var activitySummaryEntryImplementors = []string{"ActivitySummaryEntry"}

func (ec *executionContext) _ActivitySummaryEntry(ctx context.Context, sel ast.SelectionSet, obj *models.ActivitySummaryEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, activitySummaryEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ActivitySummaryEntry")
		case "activity":
			out.Values[i] = ec._ActivitySummaryEntry_activity(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "coins":
			out.Values[i] = ec._ActivitySummaryEntry_coins(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leagueImplementors = []string{"League"}

func (ec *executionContext) _League(ctx context.Context, sel ast.SelectionSet, obj *models.League) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leagueImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("League")
		case "id":
			out.Values[i] = ec._League_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec._League_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "details":
			out.Values[i] = ec._League_details(ctx, field, obj)
		case "hostedBy":
			out.Values[i] = ec._League_hostedBy(ctx, field, obj)
		case "hostedByV2":
			out.Values[i] = ec._League_hostedByV2(ctx, field, obj)
		case "registrationStart":
			out.Values[i] = ec._League_registrationStart(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationEnd":
			out.Values[i] = ec._League_registrationEnd(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "leagueStart":
			out.Values[i] = ec._League_leagueStart(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "leagueEnd":
			out.Values[i] = ec._League_leagueEnd(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationCount":
			out.Values[i] = ec._League_registrationCount(ctx, field, obj)
		case "chatRoomId":
			out.Values[i] = ec._League_chatRoomId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "currentUserParticipation":
			out.Values[i] = ec._League_currentUserParticipation(ctx, field, obj)
		case "currentUserResult":
			out.Values[i] = ec._League_currentUserResult(ctx, field, obj)
		case "registrationForm":
			out.Values[i] = ec._League_registrationForm(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leagueDetailsImplementors = []string{"LeagueDetails"}

func (ec *executionContext) _LeagueDetails(ctx context.Context, sel ast.SelectionSet, obj *models.LeagueDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leagueDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeagueDetails")
		case "about":
			out.Values[i] = ec._LeagueDetails_about(ctx, field, obj)
		case "requirements":
			out.Values[i] = ec._LeagueDetails_requirements(ctx, field, obj)
		case "instructions":
			out.Values[i] = ec._LeagueDetails_instructions(ctx, field, obj)
		case "awards":
			out.Values[i] = ec._LeagueDetails_awards(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leagueLeaderboardEntryImplementors = []string{"LeagueLeaderboardEntry"}

func (ec *executionContext) _LeagueLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, obj *models.LeagueLeaderboardEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leagueLeaderboardEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeagueLeaderboardEntry")
		case "user":
			out.Values[i] = ec._LeagueLeaderboardEntry_user(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "statikCoins":
			out.Values[i] = ec._LeagueLeaderboardEntry_statikCoins(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rank":
			out.Values[i] = ec._LeagueLeaderboardEntry_rank(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "activitySummary":
			out.Values[i] = ec._LeagueLeaderboardEntry_activitySummary(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leagueLeaderboardPageImplementors = []string{"LeagueLeaderboardPage"}

func (ec *executionContext) _LeagueLeaderboardPage(ctx context.Context, sel ast.SelectionSet, obj *models.LeagueLeaderboardPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leagueLeaderboardPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeagueLeaderboardPage")
		case "participants":
			out.Values[i] = ec._LeagueLeaderboardPage_participants(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalCount":
			out.Values[i] = ec._LeagueLeaderboardPage_totalCount(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leagueParticipantImplementors = []string{"LeagueParticipant"}

func (ec *executionContext) _LeagueParticipant(ctx context.Context, sel ast.SelectionSet, obj *models.LeagueParticipant) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leagueParticipantImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeagueParticipant")
		case "id":
			out.Values[i] = ec._LeagueParticipant_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._LeagueParticipant_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "leagueId":
			out.Values[i] = ec._LeagueParticipant_leagueId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "joinedAt":
			out.Values[i] = ec._LeagueParticipant_joinedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationData":
			out.Values[i] = ec._LeagueParticipant_registrationData(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var paginatedLeaguesImplementors = []string{"PaginatedLeagues"}

func (ec *executionContext) _PaginatedLeagues(ctx context.Context, sel ast.SelectionSet, obj *models.PaginatedLeagues) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, paginatedLeaguesImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PaginatedLeagues")
		case "league":
			out.Values[i] = ec._PaginatedLeagues_league(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalCount":
			out.Values[i] = ec._PaginatedLeagues_totalCount(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNActivitySummaryEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐActivitySummaryEntryᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.ActivitySummaryEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNActivitySummaryEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐActivitySummaryEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNActivitySummaryEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐActivitySummaryEntry(ctx context.Context, sel ast.SelectionSet, v *models.ActivitySummaryEntry) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ActivitySummaryEntry(ctx, sel, v)
}

func (ec *executionContext) unmarshalNCreateLeagueInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCreateLeagueInput(ctx context.Context, v any) (models.CreateLeagueInput, error) {
	res, err := ec.unmarshalInputCreateLeagueInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNLeague2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeague(ctx context.Context, sel ast.SelectionSet, v models.League) graphql.Marshaler {
	return ec._League(ctx, sel, &v)
}

func (ec *executionContext) marshalNLeague2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.League) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNLeague2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeague(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNLeague2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeague(ctx context.Context, sel ast.SelectionSet, v *models.League) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._League(ctx, sel, v)
}

func (ec *executionContext) marshalNLeagueLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardEntryᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.LeagueLeaderboardEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNLeagueLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNLeagueLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, v *models.LeagueLeaderboardEntry) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._LeagueLeaderboardEntry(ctx, sel, v)
}

func (ec *executionContext) marshalNLeagueLeaderboardPage2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v models.LeagueLeaderboardPage) graphql.Marshaler {
	return ec._LeagueLeaderboardPage(ctx, sel, &v)
}

func (ec *executionContext) marshalNLeagueLeaderboardPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v *models.LeagueLeaderboardPage) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._LeagueLeaderboardPage(ctx, sel, v)
}

func (ec *executionContext) unmarshalNLeagueStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueStatus(ctx context.Context, v any) (models.LeagueStatus, error) {
	var res models.LeagueStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNLeagueStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueStatus(ctx context.Context, sel ast.SelectionSet, v models.LeagueStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNLeagueStatus2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueStatusᚄ(ctx context.Context, v any) ([]models.LeagueStatus, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]models.LeagueStatus, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNLeagueStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueStatus(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalNLeagueStatus2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueStatusᚄ(ctx context.Context, sel ast.SelectionSet, v []models.LeagueStatus) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNLeagueStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueStatus(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalOJoinLeagueInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐJoinLeagueInput(ctx context.Context, v any) (*models.JoinLeagueInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputJoinLeagueInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOLeagueDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueDetails(ctx context.Context, sel ast.SelectionSet, v *models.LeagueDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeagueDetails(ctx, sel, v)
}

func (ec *executionContext) unmarshalOLeagueDetailsInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueDetailsInput(ctx context.Context, v any) (*models.LeagueDetailsInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputLeagueDetailsInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOLeagueLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, v *models.LeagueLeaderboardEntry) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeagueLeaderboardEntry(ctx, sel, v)
}

func (ec *executionContext) marshalOLeagueParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueParticipant(ctx context.Context, sel ast.SelectionSet, v *models.LeagueParticipant) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeagueParticipant(ctx, sel, v)
}

func (ec *executionContext) marshalOPaginatedLeagues2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedLeagues(ctx context.Context, sel ast.SelectionSet, v *models.PaginatedLeagues) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PaginatedLeagues(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
