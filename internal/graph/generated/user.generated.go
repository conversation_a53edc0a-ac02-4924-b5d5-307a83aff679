// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _AwardsAndAchievements_imageUrl(ctx context.Context, field graphql.CollectedField, obj *models.AwardsAndAchievements) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_AwardsAndAchievements_imageUrl(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ImageURL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_AwardsAndAchievements_imageUrl(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "AwardsAndAchievements",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _AwardsAndAchievements_link(ctx context.Context, field graphql.CollectedField, obj *models.AwardsAndAchievements) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_AwardsAndAchievements_link(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Link, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_AwardsAndAchievements_link(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "AwardsAndAchievements",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _AwardsAndAchievements_title(ctx context.Context, field graphql.CollectedField, obj *models.AwardsAndAchievements) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_AwardsAndAchievements_title(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Title, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_AwardsAndAchievements_title(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "AwardsAndAchievements",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _AwardsAndAchievements_description(ctx context.Context, field graphql.CollectedField, obj *models.AwardsAndAchievements) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_AwardsAndAchievements_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_AwardsAndAchievements_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "AwardsAndAchievements",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _BadgeAssignedEvent_initialBadge(ctx context.Context, field graphql.CollectedField, obj *models.BadgeAssignedEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_BadgeAssignedEvent_initialBadge(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InitialBadge, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.BadgeType)
	fc.Result = res
	return ec.marshalOBadgeType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBadgeType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_BadgeAssignedEvent_initialBadge(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "BadgeAssignedEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type BadgeType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _BadgeAssignedEvent_newBadge(ctx context.Context, field graphql.CollectedField, obj *models.BadgeAssignedEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_BadgeAssignedEvent_newBadge(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NewBadge, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.BadgeType)
	fc.Result = res
	return ec.marshalOBadgeType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBadgeType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_BadgeAssignedEvent_newBadge(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "BadgeAssignedEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type BadgeType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DeviceTokenRegistrationResponse_success(ctx context.Context, field graphql.CollectedField, obj *models.DeviceTokenRegistrationResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DeviceTokenRegistrationResponse_success(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Success, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DeviceTokenRegistrationResponse_success(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DeviceTokenRegistrationResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DeviceTokenRegistrationResponse_message(ctx context.Context, field graphql.CollectedField, obj *models.DeviceTokenRegistrationResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DeviceTokenRegistrationResponse_message(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Message, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DeviceTokenRegistrationResponse_message(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DeviceTokenRegistrationResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameCanceledOutput_gameId(ctx context.Context, field graphql.CollectedField, obj *models.GameCanceledOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameCanceledOutput_gameId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameCanceledOutput_gameId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameCanceledOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameCanceledOutput_creatorId(ctx context.Context, field graphql.CollectedField, obj *models.GameCanceledOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameCanceledOutput_creatorId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatorID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameCanceledOutput_creatorId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameCanceledOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameCanceledOutput_opponentId(ctx context.Context, field graphql.CollectedField, obj *models.GameCanceledOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameCanceledOutput_opponentId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.OpponentID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameCanceledOutput_opponentId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameCanceledOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameCanceledOutput_status(ctx context.Context, field graphql.CollectedField, obj *models.GameCanceledOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameCanceledOutput_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ChallengeStatus)
	fc.Result = res
	return ec.marshalOChallengeStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameCanceledOutput_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameCanceledOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ChallengeStatus does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _JoinedWeeklyLeagueEvent_leagueInfo(ctx context.Context, field graphql.CollectedField, obj *models.JoinedWeeklyLeagueEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_JoinedWeeklyLeagueEvent_leagueInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeagueInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.LeagueInfo)
	fc.Result = res
	return ec.marshalNLeagueInfo2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_JoinedWeeklyLeagueEvent_leagueInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "JoinedWeeklyLeagueEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "league":
				return ec.fieldContext_LeagueInfo_league(ctx, field)
			case "groupId":
				return ec.fieldContext_LeagueInfo_groupId(ctx, field)
			case "updatedAt":
				return ec.fieldContext_LeagueInfo_updatedAt(ctx, field)
			case "hasParticipated":
				return ec.fieldContext_LeagueInfo_hasParticipated(ctx, field)
			case "coinsTillLastWeek":
				return ec.fieldContext_LeagueInfo_coinsTillLastWeek(ctx, field)
			case "progressState":
				return ec.fieldContext_LeagueInfo_progressState(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardConnection_edges(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardConnection_edges(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Edges, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.LeaderboardEdge)
	fc.Result = res
	return ec.marshalOLeaderboardEdge2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardEdge(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardConnection_edges(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "cursor":
				return ec.fieldContext_LeaderboardEdge_cursor(ctx, field)
			case "node":
				return ec.fieldContext_LeaderboardEdge_node(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeaderboardEdge", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardConnection_pageInfo(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardConnection_pageInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.PageInfo)
	fc.Result = res
	return ec.marshalNPageInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardConnection_pageInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "hasNextPage":
				return ec.fieldContext_PageInfo_hasNextPage(ctx, field)
			case "endCursor":
				return ec.fieldContext_PageInfo_endCursor(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PageInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardEdge_cursor(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardEdge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardEdge_cursor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Cursor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardEdge_cursor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardEdge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardEdge_node(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardEdge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardEdge_node(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Node, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardEdge_node(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardEdge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueInfo_league(ctx context.Context, field graphql.CollectedField, obj *models.LeagueInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueInfo_league(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.League, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.LeagueType)
	fc.Result = res
	return ec.marshalOLeagueType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueInfo_league(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type LeagueType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueInfo_groupId(ctx context.Context, field graphql.CollectedField, obj *models.LeagueInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueInfo_groupId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GroupID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueInfo_groupId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueInfo_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.LeagueInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueInfo_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueInfo_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueInfo_hasParticipated(ctx context.Context, field graphql.CollectedField, obj *models.LeagueInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueInfo_hasParticipated(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasParticipated, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueInfo_hasParticipated(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueInfo_coinsTillLastWeek(ctx context.Context, field graphql.CollectedField, obj *models.LeagueInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueInfo_coinsTillLastWeek(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CoinsTillLastWeek, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueInfo_coinsTillLastWeek(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeagueInfo_progressState(ctx context.Context, field graphql.CollectedField, obj *models.LeagueInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeagueInfo_progressState(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProgressState, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.WeeklyLeagueProgressState)
	fc.Result = res
	return ec.marshalOWeeklyLeagueProgressState2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueProgressState(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeagueInfo_progressState(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeagueInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type WeeklyLeagueProgressState does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MyInstituteUsersPage_results(ctx context.Context, field graphql.CollectedField, obj *models.MyInstituteUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MyInstituteUsersPage_results(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Results, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.SearchUserOutput)
	fc.Result = res
	return ec.marshalNSearchUserOutput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSearchUserOutputᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MyInstituteUsersPage_results(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MyInstituteUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userPublicDetails":
				return ec.fieldContext_SearchUserOutput_userPublicDetails(ctx, field)
			case "isFollowing":
				return ec.fieldContext_SearchUserOutput_isFollowing(ctx, field)
			case "friendshipStatus":
				return ec.fieldContext_SearchUserOutput_friendshipStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type SearchUserOutput", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MyInstituteUsersPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.MyInstituteUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MyInstituteUsersPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int64)
	fc.Result = res
	return ec.marshalNInt2int64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MyInstituteUsersPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MyInstituteUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MyInstituteUsersPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.MyInstituteUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MyInstituteUsersPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int64)
	fc.Result = res
	return ec.marshalNInt2int64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MyInstituteUsersPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MyInstituteUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MyInstituteUsersPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.MyInstituteUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MyInstituteUsersPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MyInstituteUsersPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MyInstituteUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MyInstituteUsersPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.MyInstituteUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MyInstituteUsersPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int64)
	fc.Result = res
	return ec.marshalOInt2int64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MyInstituteUsersPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MyInstituteUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _OnlineUsersPage_users(ctx context.Context, field graphql.CollectedField, obj *models.OnlineUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_OnlineUsersPage_users(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Users, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.UserDetailWithActivity)
	fc.Result = res
	return ec.marshalNUserDetailWithActivity2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailWithActivityᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_OnlineUsersPage_users(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "OnlineUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userInfo":
				return ec.fieldContext_UserDetailWithActivity_userInfo(ctx, field)
			case "currActivity":
				return ec.fieldContext_UserDetailWithActivity_currActivity(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserDetailWithActivity", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _OnlineUsersPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.OnlineUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_OnlineUsersPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_OnlineUsersPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "OnlineUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _OnlineUsersPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.OnlineUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_OnlineUsersPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_OnlineUsersPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "OnlineUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _OnlineUsersPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.OnlineUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_OnlineUsersPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_OnlineUsersPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "OnlineUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _OnlineUsersPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.OnlineUsersPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_OnlineUsersPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_OnlineUsersPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "OnlineUsersPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PageInfo_hasNextPage(ctx context.Context, field graphql.CollectedField, obj *models.PageInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PageInfo_hasNextPage(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasNextPage, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PageInfo_hasNextPage(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PageInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PageInfo_endCursor(ctx context.Context, field graphql.CollectedField, obj *models.PageInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PageInfo_endCursor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndCursor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PageInfo_endCursor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PageInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PhoneNumber_countryCode(ctx context.Context, field graphql.CollectedField, obj *models.PhoneNumber) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PhoneNumber_countryCode(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CountryCode, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PhoneNumber_countryCode(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PhoneNumber",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PhoneNumber_number(ctx context.Context, field graphql.CollectedField, obj *models.PhoneNumber) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PhoneNumber_number(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Number, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PhoneNumber_number(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PhoneNumber",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RatingFixtureOutput_newRating(ctx context.Context, field graphql.CollectedField, obj *models.RatingFixtureOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RatingFixtureOutput_newRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NewRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RatingFixtureOutput_newRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RatingFixtureOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_gameId(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_gameId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_gameId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_requestedBy(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_requestedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RequestedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_requestedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_status(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_newGameId(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_newGameId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NewGameID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_newGameId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_user(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.User)
	fc.Result = res
	return ec.marshalOUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_User__id(ctx, field)
			case "email":
				return ec.fieldContext_User_email(ctx, field)
			case "token":
				return ec.fieldContext_User_token(ctx, field)
			case "name":
				return ec.fieldContext_User_name(ctx, field)
			case "username":
				return ec.fieldContext_User_username(ctx, field)
			case "bio":
				return ec.fieldContext_User_bio(ctx, field)
			case "country":
				return ec.fieldContext_User_country(ctx, field)
			case "links":
				return ec.fieldContext_User_links(ctx, field)
			case "awardsAndAchievements":
				return ec.fieldContext_User_awardsAndAchievements(ctx, field)
			case "phoneNumber":
				return ec.fieldContext_User_phoneNumber(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_User_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_User_rating(ctx, field)
			case "ratingV2":
				return ec.fieldContext_User_ratingV2(ctx, field)
			case "statikCoins":
				return ec.fieldContext_User_statikCoins(ctx, field)
			case "badge":
				return ec.fieldContext_User_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_User_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_User_isGuest(ctx, field)
			case "isBot":
				return ec.fieldContext_User_isBot(ctx, field)
			case "isShadowBanned":
				return ec.fieldContext_User_isShadowBanned(ctx, field)
			case "shadowBanStatus":
				return ec.fieldContext_User_shadowBanStatus(ctx, field)
			case "suspiciousActivity":
				return ec.fieldContext_User_suspiciousActivity(ctx, field)
			case "globalRank":
				return ec.fieldContext_User_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_User_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_User_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_User_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_User_stats(ctx, field)
			case "hasFixedRating":
				return ec.fieldContext_User_hasFixedRating(ctx, field)
			case "isSignup":
				return ec.fieldContext_User_isSignup(ctx, field)
			case "userStreaks":
				return ec.fieldContext_User_userStreaks(ctx, field)
			case "timezone":
				return ec.fieldContext_User_timezone(ctx, field)
			case "additional":
				return ec.fieldContext_User_additional(ctx, field)
			case "league":
				return ec.fieldContext_User_league(ctx, field)
			case "institutionId":
				return ec.fieldContext_User_institutionId(ctx, field)
			case "institutionName":
				return ec.fieldContext_User_institutionName(ctx, field)
			case "lastReadFeedId":
				return ec.fieldContext_User_lastReadFeedId(ctx, field)
			case "referralCode":
				return ec.fieldContext_User_referralCode(ctx, field)
			case "isReferred":
				return ec.fieldContext_User_isReferred(ctx, field)
			case "isDeleted":
				return ec.fieldContext_User_isDeleted(ctx, field)
			case "accountStatus":
				return ec.fieldContext_User_accountStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type User", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_waitingTime(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_waitingTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.WaitingTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_waitingTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RematchRequestOutput_gameType(ctx context.Context, field graphql.CollectedField, obj *models.RematchRequestOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RematchRequestOutput_gameType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RematchRequestOutput_gameType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RematchRequestOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SearchUserOutput_userPublicDetails(ctx context.Context, field graphql.CollectedField, obj *models.SearchUserOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SearchUserOutput_userPublicDetails(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserPublicDetails, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.User)
	fc.Result = res
	return ec.marshalOUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SearchUserOutput_userPublicDetails(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SearchUserOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_User__id(ctx, field)
			case "email":
				return ec.fieldContext_User_email(ctx, field)
			case "token":
				return ec.fieldContext_User_token(ctx, field)
			case "name":
				return ec.fieldContext_User_name(ctx, field)
			case "username":
				return ec.fieldContext_User_username(ctx, field)
			case "bio":
				return ec.fieldContext_User_bio(ctx, field)
			case "country":
				return ec.fieldContext_User_country(ctx, field)
			case "links":
				return ec.fieldContext_User_links(ctx, field)
			case "awardsAndAchievements":
				return ec.fieldContext_User_awardsAndAchievements(ctx, field)
			case "phoneNumber":
				return ec.fieldContext_User_phoneNumber(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_User_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_User_rating(ctx, field)
			case "ratingV2":
				return ec.fieldContext_User_ratingV2(ctx, field)
			case "statikCoins":
				return ec.fieldContext_User_statikCoins(ctx, field)
			case "badge":
				return ec.fieldContext_User_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_User_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_User_isGuest(ctx, field)
			case "isBot":
				return ec.fieldContext_User_isBot(ctx, field)
			case "isShadowBanned":
				return ec.fieldContext_User_isShadowBanned(ctx, field)
			case "shadowBanStatus":
				return ec.fieldContext_User_shadowBanStatus(ctx, field)
			case "suspiciousActivity":
				return ec.fieldContext_User_suspiciousActivity(ctx, field)
			case "globalRank":
				return ec.fieldContext_User_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_User_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_User_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_User_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_User_stats(ctx, field)
			case "hasFixedRating":
				return ec.fieldContext_User_hasFixedRating(ctx, field)
			case "isSignup":
				return ec.fieldContext_User_isSignup(ctx, field)
			case "userStreaks":
				return ec.fieldContext_User_userStreaks(ctx, field)
			case "timezone":
				return ec.fieldContext_User_timezone(ctx, field)
			case "additional":
				return ec.fieldContext_User_additional(ctx, field)
			case "league":
				return ec.fieldContext_User_league(ctx, field)
			case "institutionId":
				return ec.fieldContext_User_institutionId(ctx, field)
			case "institutionName":
				return ec.fieldContext_User_institutionName(ctx, field)
			case "lastReadFeedId":
				return ec.fieldContext_User_lastReadFeedId(ctx, field)
			case "referralCode":
				return ec.fieldContext_User_referralCode(ctx, field)
			case "isReferred":
				return ec.fieldContext_User_isReferred(ctx, field)
			case "isDeleted":
				return ec.fieldContext_User_isDeleted(ctx, field)
			case "accountStatus":
				return ec.fieldContext_User_accountStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type User", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _SearchUserOutput_isFollowing(ctx context.Context, field graphql.CollectedField, obj *models.SearchUserOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SearchUserOutput_isFollowing(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsFollowing, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SearchUserOutput_isFollowing(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SearchUserOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SearchUserOutput_friendshipStatus(ctx context.Context, field graphql.CollectedField, obj *models.SearchUserOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SearchUserOutput_friendshipStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FriendshipStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.FriendshipStatus)
	fc.Result = res
	return ec.marshalOFRIENDSHIP_STATUS2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFriendshipStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SearchUserOutput_friendshipStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SearchUserOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type FRIENDSHIP_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardEntry_user(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardEntry_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardEntry_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardEntry_statikCoins(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardEntry_statikCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardEntry_statikCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardEntry_rank(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardEntry_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardEntry_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardPage_results(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardPage_results(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Results, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.StatikCoinLeaderboardEntry)
	fc.Result = res
	return ec.marshalNStatikCoinLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardEntryᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardPage_results(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_StatikCoinLeaderboardEntry_user(ctx, field)
			case "statikCoins":
				return ec.fieldContext_StatikCoinLeaderboardEntry_statikCoins(ctx, field)
			case "rank":
				return ec.fieldContext_StatikCoinLeaderboardEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type StatikCoinLeaderboardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StatikCoinLeaderboardPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.StatikCoinLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StatikCoinLeaderboardPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StatikCoinLeaderboardPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StatikCoinLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TopPlayerEntry_user(ctx context.Context, field graphql.CollectedField, obj *models.TopPlayerEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TopPlayerEntry_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TopPlayerEntry_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TopPlayerEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _TopPlayerEntry_rating(ctx context.Context, field graphql.CollectedField, obj *models.TopPlayerEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TopPlayerEntry_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TopPlayerEntry_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TopPlayerEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TopPlayerEntry_rank(ctx context.Context, field graphql.CollectedField, obj *models.TopPlayerEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TopPlayerEntry_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TopPlayerEntry_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TopPlayerEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TopPlayersLeaderboard_globalRating(ctx context.Context, field graphql.CollectedField, obj *models.TopPlayersLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TopPlayersLeaderboard_globalRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.TopPlayerEntry)
	fc.Result = res
	return ec.marshalNTopPlayerEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayerEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TopPlayersLeaderboard_globalRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TopPlayersLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_TopPlayerEntry_user(ctx, field)
			case "rating":
				return ec.fieldContext_TopPlayerEntry_rating(ctx, field)
			case "rank":
				return ec.fieldContext_TopPlayerEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type TopPlayerEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _TopPlayersLeaderboard_memoryRating(ctx context.Context, field graphql.CollectedField, obj *models.TopPlayersLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TopPlayersLeaderboard_memoryRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MemoryRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.TopPlayerEntry)
	fc.Result = res
	return ec.marshalNTopPlayerEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayerEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TopPlayersLeaderboard_memoryRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TopPlayersLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_TopPlayerEntry_user(ctx, field)
			case "rating":
				return ec.fieldContext_TopPlayerEntry_rating(ctx, field)
			case "rank":
				return ec.fieldContext_TopPlayerEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type TopPlayerEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _TopPlayersLeaderboard_abilityRating(ctx context.Context, field graphql.CollectedField, obj *models.TopPlayersLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TopPlayersLeaderboard_abilityRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AbilityRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.TopPlayerEntry)
	fc.Result = res
	return ec.marshalNTopPlayerEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayerEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TopPlayersLeaderboard_abilityRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TopPlayersLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_TopPlayerEntry_user(ctx, field)
			case "rating":
				return ec.fieldContext_TopPlayerEntry_rating(ctx, field)
			case "rank":
				return ec.fieldContext_TopPlayerEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type TopPlayerEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User__id(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_email(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_email(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Email, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_email(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_token(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_token(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Token, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_token(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_name(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_username(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_username(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Username, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_username(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_bio(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_bio(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Bio, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_bio(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_country(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_country(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Country, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_country(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_links(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_links(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Links, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_links(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_awardsAndAchievements(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_awardsAndAchievements(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AwardsAndAchievements, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.AwardsAndAchievements)
	fc.Result = res
	return ec.marshalOAwardsAndAchievements2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievements(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_awardsAndAchievements(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "imageUrl":
				return ec.fieldContext_AwardsAndAchievements_imageUrl(ctx, field)
			case "link":
				return ec.fieldContext_AwardsAndAchievements_link(ctx, field)
			case "title":
				return ec.fieldContext_AwardsAndAchievements_title(ctx, field)
			case "description":
				return ec.fieldContext_AwardsAndAchievements_description(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type AwardsAndAchievements", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_phoneNumber(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_phoneNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PhoneNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PhoneNumber)
	fc.Result = res
	return ec.marshalOPhoneNumber2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPhoneNumber(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_phoneNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "countryCode":
				return ec.fieldContext_PhoneNumber_countryCode(ctx, field)
			case "number":
				return ec.fieldContext_PhoneNumber_number(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PhoneNumber", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_profileImageUrl(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_profileImageUrl(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProfileImageURL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_profileImageUrl(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_rating(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_ratingV2(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_ratingV2(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatingV2, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserRating)
	fc.Result = res
	return ec.marshalOUserRating2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserRating(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_ratingV2(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "globalRating":
				return ec.fieldContext_UserRating_globalRating(ctx, field)
			case "flashAnzanRating":
				return ec.fieldContext_UserRating_flashAnzanRating(ctx, field)
			case "abilityDuelsRating":
				return ec.fieldContext_UserRating_abilityDuelsRating(ctx, field)
			case "puzzleRating":
				return ec.fieldContext_UserRating_puzzleRating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserRating", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_statikCoins(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_statikCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_statikCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_badge(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_badge(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Badge, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.BadgeType)
	fc.Result = res
	return ec.marshalOBadgeType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBadgeType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_badge(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type BadgeType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_countryCode(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_countryCode(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CountryCode, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_countryCode(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_isGuest(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_isGuest(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsGuest, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_isGuest(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_isBot(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_isBot(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsBot, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_isBot(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_isShadowBanned(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_isShadowBanned(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsShadowBanned, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_isShadowBanned(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_shadowBanStatus(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_shadowBanStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShadowBanStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserShadowBanStatus)
	fc.Result = res
	return ec.marshalOUserShadowBanStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserShadowBanStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_shadowBanStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type UserShadowBanStatus does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_suspiciousActivity(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_suspiciousActivity(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SuspiciousActivity, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_suspiciousActivity(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_globalRank(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_globalRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_globalRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_previousGlobalRank(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_previousGlobalRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PreviousGlobalRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_previousGlobalRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_countryRank(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_countryRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CountryRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_countryRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_previousCountryRank(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_previousCountryRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PreviousCountryRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_previousCountryRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_stats(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserStats)
	fc.Result = res
	return ec.marshalOUserStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "ngp":
				return ec.fieldContext_UserStats_ngp(ctx, field)
			case "hr":
				return ec.fieldContext_UserStats_hr(ctx, field)
			case "followersCount":
				return ec.fieldContext_UserStats_followersCount(ctx, field)
			case "followingsCount":
				return ec.fieldContext_UserStats_followingsCount(ctx, field)
			case "friendsCount":
				return ec.fieldContext_UserStats_friendsCount(ctx, field)
			case "last10BotGames":
				return ec.fieldContext_UserStats_last10BotGames(ctx, field)
			case "games":
				return ec.fieldContext_UserStats_games(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_hasFixedRating(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_hasFixedRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasFixedRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_hasFixedRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_isSignup(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_isSignup(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsSignup, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_isSignup(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_userStreaks(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_userStreaks(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserStreaks, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserStreaks)
	fc.Result = res
	return ec.marshalOUserStreaks2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStreaks(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_userStreaks(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "currentStreak":
				return ec.fieldContext_UserStreaks_currentStreak(ctx, field)
			case "longestStreak":
				return ec.fieldContext_UserStreaks_longestStreak(ctx, field)
			case "lastPlayedDate":
				return ec.fieldContext_UserStreaks_lastPlayedDate(ctx, field)
			case "lastSevenDays":
				return ec.fieldContext_UserStreaks_lastSevenDays(ctx, field)
			case "streakHistory":
				return ec.fieldContext_UserStreaks_streakHistory(ctx, field)
			case "streakFreezers":
				return ec.fieldContext_UserStreaks_streakFreezers(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserStreaks", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_timezone(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_timezone(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Timezone, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_timezone(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_additional(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_additional(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Additional, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserAdditional)
	fc.Result = res
	return ec.marshalOUserAdditional2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAdditional(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_additional(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeSpent":
				return ec.fieldContext_UserAdditional_timeSpent(ctx, field)
			case "hasUnlockedAllGames":
				return ec.fieldContext_UserAdditional_hasUnlockedAllGames(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserAdditional", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_league(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_league(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.League, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.LeagueInfo)
	fc.Result = res
	return ec.marshalOLeagueInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_league(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "league":
				return ec.fieldContext_LeagueInfo_league(ctx, field)
			case "groupId":
				return ec.fieldContext_LeagueInfo_groupId(ctx, field)
			case "updatedAt":
				return ec.fieldContext_LeagueInfo_updatedAt(ctx, field)
			case "hasParticipated":
				return ec.fieldContext_LeagueInfo_hasParticipated(ctx, field)
			case "coinsTillLastWeek":
				return ec.fieldContext_LeagueInfo_coinsTillLastWeek(ctx, field)
			case "progressState":
				return ec.fieldContext_LeagueInfo_progressState(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_institutionId(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_institutionId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InstitutionID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_institutionId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_institutionName(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_institutionName(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InstitutionName, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_institutionName(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_lastReadFeedId(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_lastReadFeedId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastReadFeedID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_lastReadFeedId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_referralCode(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_referralCode(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ReferralCode, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_referralCode(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_isReferred(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_isReferred(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsReferred, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_isReferred(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_isDeleted(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_isDeleted(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsDeleted, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_isDeleted(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _User_accountStatus(ctx context.Context, field graphql.CollectedField, obj *models.User) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_User_accountStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AccountStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserAccountStatus)
	fc.Result = res
	return ec.marshalOUserAccountStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAccountStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_User_accountStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "User",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type UserAccountStatus does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserAdditional_timeSpent(ctx context.Context, field graphql.CollectedField, obj *models.UserAdditional) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserAdditional_timeSpent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeSpent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int64)
	fc.Result = res
	return ec.marshalOInt2ᚖint64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserAdditional_timeSpent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserAdditional",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserAdditional_hasUnlockedAllGames(ctx context.Context, field graphql.CollectedField, obj *models.UserAdditional) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserAdditional_hasUnlockedAllGames(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasUnlockedAllGames, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserAdditional_hasUnlockedAllGames(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserAdditional",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailWithActivity_userInfo(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailWithActivity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailWithActivity_userInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailWithActivity_userInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailWithActivity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailWithActivity_currActivity(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailWithActivity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailWithActivity_currActivity(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrActivity, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserActivityType)
	fc.Result = res
	return ec.marshalNUserActivityType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserActivityType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailWithActivity_currActivity(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailWithActivity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type UserActivityType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserGame_id(ctx context.Context, field graphql.CollectedField, obj *models.UserGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserGame_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserGame_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserGame_sT(ctx context.Context, field graphql.CollectedField, obj *models.UserGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserGame_sT(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ST, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserGame_sT(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserLeaderboardPage_edges(ctx context.Context, field graphql.CollectedField, obj *models.UserLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserLeaderboardPage_edges(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Edges, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.LeaderboardEdge)
	fc.Result = res
	return ec.marshalOLeaderboardEdge2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardEdge(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserLeaderboardPage_edges(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "cursor":
				return ec.fieldContext_LeaderboardEdge_cursor(ctx, field)
			case "node":
				return ec.fieldContext_LeaderboardEdge_node(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeaderboardEdge", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserLeaderboardPage_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.UserLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserLeaderboardPage_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserLeaderboardPage_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails__id(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_name(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_username(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_username(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Username, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalOString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_username(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_profileImageUrl(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProfileImageURL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_profileImageUrl(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_rating(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_badge(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_badge(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Badge, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.BadgeType)
	fc.Result = res
	return ec.marshalOBadgeType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBadgeType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_badge(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type BadgeType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_countryCode(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CountryCode, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_countryCode(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_isGuest(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsGuest, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_isGuest(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_globalRank(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_globalRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_previousGlobalRank(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PreviousGlobalRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_previousGlobalRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_countryRank(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CountryRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_countryRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_previousCountryRank(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PreviousCountryRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_previousCountryRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_stats(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserStats)
	fc.Result = res
	return ec.marshalOUserStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "ngp":
				return ec.fieldContext_UserStats_ngp(ctx, field)
			case "hr":
				return ec.fieldContext_UserStats_hr(ctx, field)
			case "followersCount":
				return ec.fieldContext_UserStats_followersCount(ctx, field)
			case "followingsCount":
				return ec.fieldContext_UserStats_followingsCount(ctx, field)
			case "friendsCount":
				return ec.fieldContext_UserStats_friendsCount(ctx, field)
			case "last10BotGames":
				return ec.fieldContext_UserStats_last10BotGames(ctx, field)
			case "games":
				return ec.fieldContext_UserStats_games(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_userStreaks(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserStreaks, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserStreaks)
	fc.Result = res
	return ec.marshalOUserStreaks2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStreaks(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_userStreaks(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "currentStreak":
				return ec.fieldContext_UserStreaks_currentStreak(ctx, field)
			case "longestStreak":
				return ec.fieldContext_UserStreaks_longestStreak(ctx, field)
			case "lastPlayedDate":
				return ec.fieldContext_UserStreaks_lastPlayedDate(ctx, field)
			case "lastSevenDays":
				return ec.fieldContext_UserStreaks_lastSevenDays(ctx, field)
			case "streakHistory":
				return ec.fieldContext_UserStreaks_streakHistory(ctx, field)
			case "streakFreezers":
				return ec.fieldContext_UserStreaks_streakFreezers(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserStreaks", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_ratingV2(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatingV2, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserRating)
	fc.Result = res
	return ec.marshalOUserRating2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserRating(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_ratingV2(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "globalRating":
				return ec.fieldContext_UserRating_globalRating(ctx, field)
			case "flashAnzanRating":
				return ec.fieldContext_UserRating_flashAnzanRating(ctx, field)
			case "abilityDuelsRating":
				return ec.fieldContext_UserRating_abilityDuelsRating(ctx, field)
			case "puzzleRating":
				return ec.fieldContext_UserRating_puzzleRating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserRating", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPublicDetails_institutionId(ctx context.Context, field graphql.CollectedField, obj *models.UserPublicDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InstitutionID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPublicDetails_institutionId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPublicDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRating_globalRating(ctx context.Context, field graphql.CollectedField, obj *models.UserRating) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRating_globalRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRating_globalRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRating",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRating_flashAnzanRating(ctx context.Context, field graphql.CollectedField, obj *models.UserRating) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRating_flashAnzanRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FlashAnzanRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRating_flashAnzanRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRating",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRating_abilityDuelsRating(ctx context.Context, field graphql.CollectedField, obj *models.UserRating) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRating_abilityDuelsRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AbilityDuelsRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRating_abilityDuelsRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRating",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRating_puzzleRating(ctx context.Context, field graphql.CollectedField, obj *models.UserRating) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRating_puzzleRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRating_puzzleRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRating",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_id(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_userId(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_submissions(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_submissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Submissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*int)
	fc.Result = res
	return ec.marshalNInt2ᚕᚖintᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_submissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_userScore(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_userScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_userScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_timeTaken(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_timeTaken(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeTaken, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_timeTaken(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalOTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalOTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_currentRating(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_currentRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_currentRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_proposedRating(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_proposedRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProposedRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_proposedRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserRatingFixtureSubmission_userStance(ctx context.Context, field graphql.CollectedField, obj *models.UserRatingFixtureSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserRatingFixtureSubmission_userStance(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserStance, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.UserStance)
	fc.Result = res
	return ec.marshalNUserStance2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStance(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserRatingFixtureSubmission_userStance(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserRatingFixtureSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type UserStance does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_ngp(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_ngp(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Ngp, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_ngp(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_hr(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_hr(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Hr, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_hr(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_followersCount(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_followersCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FollowersCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_followersCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_followingsCount(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_followingsCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FollowingsCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_followingsCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_friendsCount(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_friendsCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FriendsCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_friendsCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_last10BotGames(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_last10BotGames(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Last10BotGames, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserGame)
	fc.Result = res
	return ec.marshalOUserGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_last10BotGames(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_UserGame_id(ctx, field)
			case "sT":
				return ec.fieldContext_UserGame_sT(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserGame", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserStats_games(ctx context.Context, field graphql.CollectedField, obj *models.UserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserStats_games(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Games, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserGame)
	fc.Result = res
	return ec.marshalOUserGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserStats_games(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_UserGame_id(ctx, field)
			case "sT":
				return ec.fieldContext_UserGame_sT(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserGame", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UsersWeeklyStatikCoinsOutput_totalCoins(ctx context.Context, field graphql.CollectedField, obj *models.UsersWeeklyStatikCoinsOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UsersWeeklyStatikCoinsOutput_totalCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UsersWeeklyStatikCoinsOutput_totalCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UsersWeeklyStatikCoinsOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UsersWeeklyStatikCoinsOutput_dailyCoins(ctx context.Context, field graphql.CollectedField, obj *models.UsersWeeklyStatikCoinsOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UsersWeeklyStatikCoinsOutput_dailyCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DailyCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalNInt2ᚕintᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UsersWeeklyStatikCoinsOutput_dailyCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UsersWeeklyStatikCoinsOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardEntry_user(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardEntry_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardEntry_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardEntry_statikCoins(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardEntry_statikCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardEntry_statikCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardEntry_rank(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardEntry_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardEntry_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardEntry_progressState(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardEntry_progressState(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProgressState, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.WeeklyLeagueProgressState)
	fc.Result = res
	return ec.marshalOWeeklyLeagueProgressState2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueProgressState(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardEntry_progressState(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type WeeklyLeagueProgressState does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage_results(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardPage_results(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Results, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.WeeklyLeagueLeaderboardEntry)
	fc.Result = res
	return ec.marshalNWeeklyLeagueLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueLeaderboardEntryᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardPage_results(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_WeeklyLeagueLeaderboardEntry_user(ctx, field)
			case "statikCoins":
				return ec.fieldContext_WeeklyLeagueLeaderboardEntry_statikCoins(ctx, field)
			case "rank":
				return ec.fieldContext_WeeklyLeagueLeaderboardEntry_rank(ctx, field)
			case "progressState":
				return ec.fieldContext_WeeklyLeagueLeaderboardEntry_progressState(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type WeeklyLeagueLeaderboardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage_currentUserLeague(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardPage_currentUserLeague(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserLeague, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.LeagueInfo)
	fc.Result = res
	return ec.marshalOLeagueInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardPage_currentUserLeague(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "league":
				return ec.fieldContext_LeagueInfo_league(ctx, field)
			case "groupId":
				return ec.fieldContext_LeagueInfo_groupId(ctx, field)
			case "updatedAt":
				return ec.fieldContext_LeagueInfo_updatedAt(ctx, field)
			case "hasParticipated":
				return ec.fieldContext_LeagueInfo_hasParticipated(ctx, field)
			case "coinsTillLastWeek":
				return ec.fieldContext_LeagueInfo_coinsTillLastWeek(ctx, field)
			case "progressState":
				return ec.fieldContext_LeagueInfo_progressState(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeagueInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.WeeklyLeagueLeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_WeeklyLeagueLeaderboardPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_WeeklyLeagueLeaderboardPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "WeeklyLeagueLeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputAppleSignInFullNameInput(ctx context.Context, obj any) (models.AppleSignInFullNameInput, error) {
	var it models.AppleSignInFullNameInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"familyName", "givenName", "middleName", "namePrefix", "nameSuffix", "nickname"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "familyName":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("familyName"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.FamilyName = data
		case "givenName":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("givenName"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.GivenName = data
		case "middleName":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("middleName"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.MiddleName = data
		case "namePrefix":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("namePrefix"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.NamePrefix = data
		case "nameSuffix":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("nameSuffix"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.NameSuffix = data
		case "nickname":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("nickname"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Nickname = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputAppleSignInInput(ctx context.Context, obj any) (models.AppleSignInInput, error) {
	var it models.AppleSignInInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"authorizationCode", "email", "fullName", "identityToken", "realUserStatus", "state", "user"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "authorizationCode":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("authorizationCode"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.AuthorizationCode = data
		case "email":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("email"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Email = data
		case "fullName":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("fullName"))
			data, err := ec.unmarshalOAppleSignInFullNameInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAppleSignInFullNameInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.FullName = data
		case "identityToken":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("identityToken"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.IdentityToken = data
		case "realUserStatus":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("realUserStatus"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.RealUserStatus = data
		case "state":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("state"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.State = data
		case "user":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("user"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.User = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputAwardsAndAchievementsInput(ctx context.Context, obj any) (models.AwardsAndAchievementsInput, error) {
	var it models.AwardsAndAchievementsInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"imageUrl", "link", "title", "description"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "imageUrl":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("imageUrl"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.ImageURL = data
		case "link":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("link"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Link = data
		case "title":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("title"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Title = data
		case "description":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("description"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Description = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputUpdateUserInput(ctx context.Context, obj any) (models.UpdateUserInput, error) {
	var it models.UpdateUserInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"name", "profileImageUrl", "countryCode", "timezone", "username", "bio", "country", "links", "awardsAndAchievements", "institutionId", "institutionName"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "profileImageUrl":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("profileImageUrl"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.ProfileImageURL = data
		case "countryCode":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("countryCode"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.CountryCode = data
		case "timezone":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timezone"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Timezone = data
		case "username":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("username"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Username = data
		case "bio":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("bio"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Bio = data
		case "country":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("country"))
			data, err := ec.unmarshalOString2ᚕᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Country = data
		case "links":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("links"))
			data, err := ec.unmarshalOString2ᚕᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Links = data
		case "awardsAndAchievements":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("awardsAndAchievements"))
			data, err := ec.unmarshalOAwardsAndAchievementsInput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievementsInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.AwardsAndAchievements = data
		case "institutionId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("institutionId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.InstitutionID = data
		case "institutionName":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("institutionName"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.InstitutionName = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputUserInput(ctx context.Context, obj any) (models.UserInput, error) {
	var it models.UserInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"email", "name", "password", "confirm"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "email":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("email"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Email = data
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "password":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("password"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Password = data
		case "confirm":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("confirm"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Confirm = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

func (ec *executionContext) _UserEvent(ctx context.Context, sel ast.SelectionSet, obj models.UserEvent) graphql.Marshaler {
	switch obj := (obj).(type) {
	case nil:
		return graphql.Null
	case *models.SearchSubscriptionOutput:
		if obj == nil {
			return graphql.Null
		}
		return ec._SearchSubscriptionOutput(ctx, sel, obj)
	case *models.RematchRequestOutput:
		if obj == nil {
			return graphql.Null
		}
		return ec._RematchRequestOutput(ctx, sel, obj)
	case models.RatingFixtureOutput:
		return ec._RatingFixtureOutput(ctx, sel, &obj)
	case *models.RatingFixtureOutput:
		if obj == nil {
			return graphql.Null
		}
		return ec._RatingFixtureOutput(ctx, sel, obj)
	case models.JoinedWeeklyLeagueEvent:
		return ec._JoinedWeeklyLeagueEvent(ctx, sel, &obj)
	case *models.JoinedWeeklyLeagueEvent:
		if obj == nil {
			return graphql.Null
		}
		return ec._JoinedWeeklyLeagueEvent(ctx, sel, obj)
	case models.GameCanceledOutput:
		return ec._GameCanceledOutput(ctx, sel, &obj)
	case *models.GameCanceledOutput:
		if obj == nil {
			return graphql.Null
		}
		return ec._GameCanceledOutput(ctx, sel, obj)
	case *models.ChallengeOutput:
		if obj == nil {
			return graphql.Null
		}
		return ec._ChallengeOutput(ctx, sel, obj)
	case *models.BadgeAssignedEvent:
		if obj == nil {
			return graphql.Null
		}
		return ec._BadgeAssignedEvent(ctx, sel, obj)
	default:
		panic(fmt.Errorf("unexpected type %T", obj))
	}
}

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var awardsAndAchievementsImplementors = []string{"AwardsAndAchievements"}

func (ec *executionContext) _AwardsAndAchievements(ctx context.Context, sel ast.SelectionSet, obj *models.AwardsAndAchievements) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, awardsAndAchievementsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("AwardsAndAchievements")
		case "imageUrl":
			out.Values[i] = ec._AwardsAndAchievements_imageUrl(ctx, field, obj)
		case "link":
			out.Values[i] = ec._AwardsAndAchievements_link(ctx, field, obj)
		case "title":
			out.Values[i] = ec._AwardsAndAchievements_title(ctx, field, obj)
		case "description":
			out.Values[i] = ec._AwardsAndAchievements_description(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var badgeAssignedEventImplementors = []string{"BadgeAssignedEvent", "UserEvent"}

func (ec *executionContext) _BadgeAssignedEvent(ctx context.Context, sel ast.SelectionSet, obj *models.BadgeAssignedEvent) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, badgeAssignedEventImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("BadgeAssignedEvent")
		case "initialBadge":
			out.Values[i] = ec._BadgeAssignedEvent_initialBadge(ctx, field, obj)
		case "newBadge":
			out.Values[i] = ec._BadgeAssignedEvent_newBadge(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var deviceTokenRegistrationResponseImplementors = []string{"DeviceTokenRegistrationResponse"}

func (ec *executionContext) _DeviceTokenRegistrationResponse(ctx context.Context, sel ast.SelectionSet, obj *models.DeviceTokenRegistrationResponse) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, deviceTokenRegistrationResponseImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("DeviceTokenRegistrationResponse")
		case "success":
			out.Values[i] = ec._DeviceTokenRegistrationResponse_success(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "message":
			out.Values[i] = ec._DeviceTokenRegistrationResponse_message(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameCanceledOutputImplementors = []string{"GameCanceledOutput", "UserEvent"}

func (ec *executionContext) _GameCanceledOutput(ctx context.Context, sel ast.SelectionSet, obj *models.GameCanceledOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameCanceledOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameCanceledOutput")
		case "gameId":
			out.Values[i] = ec._GameCanceledOutput_gameId(ctx, field, obj)
		case "creatorId":
			out.Values[i] = ec._GameCanceledOutput_creatorId(ctx, field, obj)
		case "opponentId":
			out.Values[i] = ec._GameCanceledOutput_opponentId(ctx, field, obj)
		case "status":
			out.Values[i] = ec._GameCanceledOutput_status(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var joinedWeeklyLeagueEventImplementors = []string{"JoinedWeeklyLeagueEvent", "UserEvent"}

func (ec *executionContext) _JoinedWeeklyLeagueEvent(ctx context.Context, sel ast.SelectionSet, obj *models.JoinedWeeklyLeagueEvent) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, joinedWeeklyLeagueEventImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("JoinedWeeklyLeagueEvent")
		case "leagueInfo":
			out.Values[i] = ec._JoinedWeeklyLeagueEvent_leagueInfo(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leaderboardConnectionImplementors = []string{"LeaderboardConnection"}

func (ec *executionContext) _LeaderboardConnection(ctx context.Context, sel ast.SelectionSet, obj *models.LeaderboardConnection) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leaderboardConnectionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeaderboardConnection")
		case "edges":
			out.Values[i] = ec._LeaderboardConnection_edges(ctx, field, obj)
		case "pageInfo":
			out.Values[i] = ec._LeaderboardConnection_pageInfo(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leaderboardEdgeImplementors = []string{"LeaderboardEdge"}

func (ec *executionContext) _LeaderboardEdge(ctx context.Context, sel ast.SelectionSet, obj *models.LeaderboardEdge) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leaderboardEdgeImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeaderboardEdge")
		case "cursor":
			out.Values[i] = ec._LeaderboardEdge_cursor(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "node":
			out.Values[i] = ec._LeaderboardEdge_node(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leagueInfoImplementors = []string{"LeagueInfo"}

func (ec *executionContext) _LeagueInfo(ctx context.Context, sel ast.SelectionSet, obj *models.LeagueInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leagueInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeagueInfo")
		case "league":
			out.Values[i] = ec._LeagueInfo_league(ctx, field, obj)
		case "groupId":
			out.Values[i] = ec._LeagueInfo_groupId(ctx, field, obj)
		case "updatedAt":
			out.Values[i] = ec._LeagueInfo_updatedAt(ctx, field, obj)
		case "hasParticipated":
			out.Values[i] = ec._LeagueInfo_hasParticipated(ctx, field, obj)
		case "coinsTillLastWeek":
			out.Values[i] = ec._LeagueInfo_coinsTillLastWeek(ctx, field, obj)
		case "progressState":
			out.Values[i] = ec._LeagueInfo_progressState(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var myInstituteUsersPageImplementors = []string{"MyInstituteUsersPage"}

func (ec *executionContext) _MyInstituteUsersPage(ctx context.Context, sel ast.SelectionSet, obj *models.MyInstituteUsersPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, myInstituteUsersPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MyInstituteUsersPage")
		case "results":
			out.Values[i] = ec._MyInstituteUsersPage_results(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageNumber":
			out.Values[i] = ec._MyInstituteUsersPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._MyInstituteUsersPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._MyInstituteUsersPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._MyInstituteUsersPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var onlineUsersPageImplementors = []string{"OnlineUsersPage"}

func (ec *executionContext) _OnlineUsersPage(ctx context.Context, sel ast.SelectionSet, obj *models.OnlineUsersPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, onlineUsersPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("OnlineUsersPage")
		case "users":
			out.Values[i] = ec._OnlineUsersPage_users(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageNumber":
			out.Values[i] = ec._OnlineUsersPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._OnlineUsersPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._OnlineUsersPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._OnlineUsersPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var pageInfoImplementors = []string{"PageInfo"}

func (ec *executionContext) _PageInfo(ctx context.Context, sel ast.SelectionSet, obj *models.PageInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, pageInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PageInfo")
		case "hasNextPage":
			out.Values[i] = ec._PageInfo_hasNextPage(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "endCursor":
			out.Values[i] = ec._PageInfo_endCursor(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var phoneNumberImplementors = []string{"PhoneNumber"}

func (ec *executionContext) _PhoneNumber(ctx context.Context, sel ast.SelectionSet, obj *models.PhoneNumber) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, phoneNumberImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PhoneNumber")
		case "countryCode":
			out.Values[i] = ec._PhoneNumber_countryCode(ctx, field, obj)
		case "number":
			out.Values[i] = ec._PhoneNumber_number(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var ratingFixtureOutputImplementors = []string{"RatingFixtureOutput", "UserEvent"}

func (ec *executionContext) _RatingFixtureOutput(ctx context.Context, sel ast.SelectionSet, obj *models.RatingFixtureOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, ratingFixtureOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("RatingFixtureOutput")
		case "newRating":
			out.Values[i] = ec._RatingFixtureOutput_newRating(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var rematchRequestOutputImplementors = []string{"RematchRequestOutput", "UserEvent"}

func (ec *executionContext) _RematchRequestOutput(ctx context.Context, sel ast.SelectionSet, obj *models.RematchRequestOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, rematchRequestOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("RematchRequestOutput")
		case "gameId":
			out.Values[i] = ec._RematchRequestOutput_gameId(ctx, field, obj)
		case "requestedBy":
			out.Values[i] = ec._RematchRequestOutput_requestedBy(ctx, field, obj)
		case "status":
			out.Values[i] = ec._RematchRequestOutput_status(ctx, field, obj)
		case "newGameId":
			out.Values[i] = ec._RematchRequestOutput_newGameId(ctx, field, obj)
		case "user":
			out.Values[i] = ec._RematchRequestOutput_user(ctx, field, obj)
		case "waitingTime":
			out.Values[i] = ec._RematchRequestOutput_waitingTime(ctx, field, obj)
		case "gameType":
			out.Values[i] = ec._RematchRequestOutput_gameType(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var searchUserOutputImplementors = []string{"SearchUserOutput"}

func (ec *executionContext) _SearchUserOutput(ctx context.Context, sel ast.SelectionSet, obj *models.SearchUserOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, searchUserOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("SearchUserOutput")
		case "userPublicDetails":
			out.Values[i] = ec._SearchUserOutput_userPublicDetails(ctx, field, obj)
		case "isFollowing":
			out.Values[i] = ec._SearchUserOutput_isFollowing(ctx, field, obj)
		case "friendshipStatus":
			out.Values[i] = ec._SearchUserOutput_friendshipStatus(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var statikCoinLeaderboardEntryImplementors = []string{"StatikCoinLeaderboardEntry"}

func (ec *executionContext) _StatikCoinLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, obj *models.StatikCoinLeaderboardEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, statikCoinLeaderboardEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("StatikCoinLeaderboardEntry")
		case "user":
			out.Values[i] = ec._StatikCoinLeaderboardEntry_user(ctx, field, obj)
		case "statikCoins":
			out.Values[i] = ec._StatikCoinLeaderboardEntry_statikCoins(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._StatikCoinLeaderboardEntry_rank(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var statikCoinLeaderboardPageImplementors = []string{"StatikCoinLeaderboardPage"}

func (ec *executionContext) _StatikCoinLeaderboardPage(ctx context.Context, sel ast.SelectionSet, obj *models.StatikCoinLeaderboardPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, statikCoinLeaderboardPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("StatikCoinLeaderboardPage")
		case "results":
			out.Values[i] = ec._StatikCoinLeaderboardPage_results(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageNumber":
			out.Values[i] = ec._StatikCoinLeaderboardPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._StatikCoinLeaderboardPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._StatikCoinLeaderboardPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._StatikCoinLeaderboardPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var topPlayerEntryImplementors = []string{"TopPlayerEntry"}

func (ec *executionContext) _TopPlayerEntry(ctx context.Context, sel ast.SelectionSet, obj *models.TopPlayerEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, topPlayerEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("TopPlayerEntry")
		case "user":
			out.Values[i] = ec._TopPlayerEntry_user(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rating":
			out.Values[i] = ec._TopPlayerEntry_rating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rank":
			out.Values[i] = ec._TopPlayerEntry_rank(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var topPlayersLeaderboardImplementors = []string{"TopPlayersLeaderboard"}

func (ec *executionContext) _TopPlayersLeaderboard(ctx context.Context, sel ast.SelectionSet, obj *models.TopPlayersLeaderboard) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, topPlayersLeaderboardImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("TopPlayersLeaderboard")
		case "globalRating":
			out.Values[i] = ec._TopPlayersLeaderboard_globalRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "memoryRating":
			out.Values[i] = ec._TopPlayersLeaderboard_memoryRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "abilityRating":
			out.Values[i] = ec._TopPlayersLeaderboard_abilityRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userImplementors = []string{"User"}

func (ec *executionContext) _User(ctx context.Context, sel ast.SelectionSet, obj *models.User) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("User")
		case "_id":
			out.Values[i] = ec._User__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "email":
			out.Values[i] = ec._User_email(ctx, field, obj)
		case "token":
			out.Values[i] = ec._User_token(ctx, field, obj)
		case "name":
			out.Values[i] = ec._User_name(ctx, field, obj)
		case "username":
			out.Values[i] = ec._User_username(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "bio":
			out.Values[i] = ec._User_bio(ctx, field, obj)
		case "country":
			out.Values[i] = ec._User_country(ctx, field, obj)
		case "links":
			out.Values[i] = ec._User_links(ctx, field, obj)
		case "awardsAndAchievements":
			out.Values[i] = ec._User_awardsAndAchievements(ctx, field, obj)
		case "phoneNumber":
			out.Values[i] = ec._User_phoneNumber(ctx, field, obj)
		case "profileImageUrl":
			out.Values[i] = ec._User_profileImageUrl(ctx, field, obj)
		case "rating":
			out.Values[i] = ec._User_rating(ctx, field, obj)
		case "ratingV2":
			out.Values[i] = ec._User_ratingV2(ctx, field, obj)
		case "statikCoins":
			out.Values[i] = ec._User_statikCoins(ctx, field, obj)
		case "badge":
			out.Values[i] = ec._User_badge(ctx, field, obj)
		case "countryCode":
			out.Values[i] = ec._User_countryCode(ctx, field, obj)
		case "isGuest":
			out.Values[i] = ec._User_isGuest(ctx, field, obj)
		case "isBot":
			out.Values[i] = ec._User_isBot(ctx, field, obj)
		case "isShadowBanned":
			out.Values[i] = ec._User_isShadowBanned(ctx, field, obj)
		case "shadowBanStatus":
			out.Values[i] = ec._User_shadowBanStatus(ctx, field, obj)
		case "suspiciousActivity":
			out.Values[i] = ec._User_suspiciousActivity(ctx, field, obj)
		case "globalRank":
			out.Values[i] = ec._User_globalRank(ctx, field, obj)
		case "previousGlobalRank":
			out.Values[i] = ec._User_previousGlobalRank(ctx, field, obj)
		case "countryRank":
			out.Values[i] = ec._User_countryRank(ctx, field, obj)
		case "previousCountryRank":
			out.Values[i] = ec._User_previousCountryRank(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._User_stats(ctx, field, obj)
		case "hasFixedRating":
			out.Values[i] = ec._User_hasFixedRating(ctx, field, obj)
		case "isSignup":
			out.Values[i] = ec._User_isSignup(ctx, field, obj)
		case "userStreaks":
			out.Values[i] = ec._User_userStreaks(ctx, field, obj)
		case "timezone":
			out.Values[i] = ec._User_timezone(ctx, field, obj)
		case "additional":
			out.Values[i] = ec._User_additional(ctx, field, obj)
		case "league":
			out.Values[i] = ec._User_league(ctx, field, obj)
		case "institutionId":
			out.Values[i] = ec._User_institutionId(ctx, field, obj)
		case "institutionName":
			out.Values[i] = ec._User_institutionName(ctx, field, obj)
		case "lastReadFeedId":
			out.Values[i] = ec._User_lastReadFeedId(ctx, field, obj)
		case "referralCode":
			out.Values[i] = ec._User_referralCode(ctx, field, obj)
		case "isReferred":
			out.Values[i] = ec._User_isReferred(ctx, field, obj)
		case "isDeleted":
			out.Values[i] = ec._User_isDeleted(ctx, field, obj)
		case "accountStatus":
			out.Values[i] = ec._User_accountStatus(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userAdditionalImplementors = []string{"UserAdditional"}

func (ec *executionContext) _UserAdditional(ctx context.Context, sel ast.SelectionSet, obj *models.UserAdditional) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userAdditionalImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserAdditional")
		case "timeSpent":
			out.Values[i] = ec._UserAdditional_timeSpent(ctx, field, obj)
		case "hasUnlockedAllGames":
			out.Values[i] = ec._UserAdditional_hasUnlockedAllGames(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userDetailWithActivityImplementors = []string{"UserDetailWithActivity"}

func (ec *executionContext) _UserDetailWithActivity(ctx context.Context, sel ast.SelectionSet, obj *models.UserDetailWithActivity) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userDetailWithActivityImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserDetailWithActivity")
		case "userInfo":
			out.Values[i] = ec._UserDetailWithActivity_userInfo(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "currActivity":
			out.Values[i] = ec._UserDetailWithActivity_currActivity(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userGameImplementors = []string{"UserGame"}

func (ec *executionContext) _UserGame(ctx context.Context, sel ast.SelectionSet, obj *models.UserGame) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userGameImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserGame")
		case "id":
			out.Values[i] = ec._UserGame_id(ctx, field, obj)
		case "sT":
			out.Values[i] = ec._UserGame_sT(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userLeaderboardPageImplementors = []string{"UserLeaderboardPage"}

func (ec *executionContext) _UserLeaderboardPage(ctx context.Context, sel ast.SelectionSet, obj *models.UserLeaderboardPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userLeaderboardPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserLeaderboardPage")
		case "edges":
			out.Values[i] = ec._UserLeaderboardPage_edges(ctx, field, obj)
		case "totalCount":
			out.Values[i] = ec._UserLeaderboardPage_totalCount(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPublicDetailsImplementors = []string{"UserPublicDetails"}

func (ec *executionContext) _UserPublicDetails(ctx context.Context, sel ast.SelectionSet, obj *models.UserPublicDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPublicDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPublicDetails")
		case "_id":
			out.Values[i] = ec._UserPublicDetails__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec._UserPublicDetails_name(ctx, field, obj)
		case "username":
			out.Values[i] = ec._UserPublicDetails_username(ctx, field, obj)
		case "profileImageUrl":
			out.Values[i] = ec._UserPublicDetails_profileImageUrl(ctx, field, obj)
		case "rating":
			out.Values[i] = ec._UserPublicDetails_rating(ctx, field, obj)
		case "badge":
			out.Values[i] = ec._UserPublicDetails_badge(ctx, field, obj)
		case "countryCode":
			out.Values[i] = ec._UserPublicDetails_countryCode(ctx, field, obj)
		case "isGuest":
			out.Values[i] = ec._UserPublicDetails_isGuest(ctx, field, obj)
		case "globalRank":
			out.Values[i] = ec._UserPublicDetails_globalRank(ctx, field, obj)
		case "previousGlobalRank":
			out.Values[i] = ec._UserPublicDetails_previousGlobalRank(ctx, field, obj)
		case "countryRank":
			out.Values[i] = ec._UserPublicDetails_countryRank(ctx, field, obj)
		case "previousCountryRank":
			out.Values[i] = ec._UserPublicDetails_previousCountryRank(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._UserPublicDetails_stats(ctx, field, obj)
		case "userStreaks":
			out.Values[i] = ec._UserPublicDetails_userStreaks(ctx, field, obj)
		case "ratingV2":
			out.Values[i] = ec._UserPublicDetails_ratingV2(ctx, field, obj)
		case "institutionId":
			out.Values[i] = ec._UserPublicDetails_institutionId(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userRatingImplementors = []string{"UserRating"}

func (ec *executionContext) _UserRating(ctx context.Context, sel ast.SelectionSet, obj *models.UserRating) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userRatingImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserRating")
		case "globalRating":
			out.Values[i] = ec._UserRating_globalRating(ctx, field, obj)
		case "flashAnzanRating":
			out.Values[i] = ec._UserRating_flashAnzanRating(ctx, field, obj)
		case "abilityDuelsRating":
			out.Values[i] = ec._UserRating_abilityDuelsRating(ctx, field, obj)
		case "puzzleRating":
			out.Values[i] = ec._UserRating_puzzleRating(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userRatingFixtureSubmissionImplementors = []string{"UserRatingFixtureSubmission"}

func (ec *executionContext) _UserRatingFixtureSubmission(ctx context.Context, sel ast.SelectionSet, obj *models.UserRatingFixtureSubmission) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userRatingFixtureSubmissionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserRatingFixtureSubmission")
		case "id":
			out.Values[i] = ec._UserRatingFixtureSubmission_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._UserRatingFixtureSubmission_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "submissions":
			out.Values[i] = ec._UserRatingFixtureSubmission_submissions(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userScore":
			out.Values[i] = ec._UserRatingFixtureSubmission_userScore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "timeTaken":
			out.Values[i] = ec._UserRatingFixtureSubmission_timeTaken(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._UserRatingFixtureSubmission_createdAt(ctx, field, obj)
		case "updatedAt":
			out.Values[i] = ec._UserRatingFixtureSubmission_updatedAt(ctx, field, obj)
		case "currentRating":
			out.Values[i] = ec._UserRatingFixtureSubmission_currentRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "proposedRating":
			out.Values[i] = ec._UserRatingFixtureSubmission_proposedRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userStance":
			out.Values[i] = ec._UserRatingFixtureSubmission_userStance(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userStatsImplementors = []string{"UserStats"}

func (ec *executionContext) _UserStats(ctx context.Context, sel ast.SelectionSet, obj *models.UserStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserStats")
		case "ngp":
			out.Values[i] = ec._UserStats_ngp(ctx, field, obj)
		case "hr":
			out.Values[i] = ec._UserStats_hr(ctx, field, obj)
		case "followersCount":
			out.Values[i] = ec._UserStats_followersCount(ctx, field, obj)
		case "followingsCount":
			out.Values[i] = ec._UserStats_followingsCount(ctx, field, obj)
		case "friendsCount":
			out.Values[i] = ec._UserStats_friendsCount(ctx, field, obj)
		case "last10BotGames":
			out.Values[i] = ec._UserStats_last10BotGames(ctx, field, obj)
		case "games":
			out.Values[i] = ec._UserStats_games(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var usersWeeklyStatikCoinsOutputImplementors = []string{"UsersWeeklyStatikCoinsOutput"}

func (ec *executionContext) _UsersWeeklyStatikCoinsOutput(ctx context.Context, sel ast.SelectionSet, obj *models.UsersWeeklyStatikCoinsOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, usersWeeklyStatikCoinsOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UsersWeeklyStatikCoinsOutput")
		case "totalCoins":
			out.Values[i] = ec._UsersWeeklyStatikCoinsOutput_totalCoins(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "dailyCoins":
			out.Values[i] = ec._UsersWeeklyStatikCoinsOutput_dailyCoins(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var weeklyLeagueLeaderboardEntryImplementors = []string{"WeeklyLeagueLeaderboardEntry"}

func (ec *executionContext) _WeeklyLeagueLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, obj *models.WeeklyLeagueLeaderboardEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, weeklyLeagueLeaderboardEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("WeeklyLeagueLeaderboardEntry")
		case "user":
			out.Values[i] = ec._WeeklyLeagueLeaderboardEntry_user(ctx, field, obj)
		case "statikCoins":
			out.Values[i] = ec._WeeklyLeagueLeaderboardEntry_statikCoins(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._WeeklyLeagueLeaderboardEntry_rank(ctx, field, obj)
		case "progressState":
			out.Values[i] = ec._WeeklyLeagueLeaderboardEntry_progressState(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var weeklyLeagueLeaderboardPageImplementors = []string{"WeeklyLeagueLeaderboardPage"}

func (ec *executionContext) _WeeklyLeagueLeaderboardPage(ctx context.Context, sel ast.SelectionSet, obj *models.WeeklyLeagueLeaderboardPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, weeklyLeagueLeaderboardPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("WeeklyLeagueLeaderboardPage")
		case "results":
			out.Values[i] = ec._WeeklyLeagueLeaderboardPage_results(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "currentUserLeague":
			out.Values[i] = ec._WeeklyLeagueLeaderboardPage_currentUserLeague(ctx, field, obj)
		case "pageNumber":
			out.Values[i] = ec._WeeklyLeagueLeaderboardPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._WeeklyLeagueLeaderboardPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._WeeklyLeagueLeaderboardPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._WeeklyLeagueLeaderboardPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNAppleSignInInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAppleSignInInput(ctx context.Context, v any) (models.AppleSignInInput, error) {
	res, err := ec.unmarshalInputAppleSignInInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNLeagueInfo2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueInfo(ctx context.Context, sel ast.SelectionSet, v models.LeagueInfo) graphql.Marshaler {
	return ec._LeagueInfo(ctx, sel, &v)
}

func (ec *executionContext) marshalNOnlineUsersPage2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐOnlineUsersPage(ctx context.Context, sel ast.SelectionSet, v models.OnlineUsersPage) graphql.Marshaler {
	return ec._OnlineUsersPage(ctx, sel, &v)
}

func (ec *executionContext) marshalNOnlineUsersPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐOnlineUsersPage(ctx context.Context, sel ast.SelectionSet, v *models.OnlineUsersPage) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._OnlineUsersPage(ctx, sel, v)
}

func (ec *executionContext) marshalNPageInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfo(ctx context.Context, sel ast.SelectionSet, v *models.PageInfo) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PageInfo(ctx, sel, v)
}

func (ec *executionContext) marshalNRematchRequestOutput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRematchRequestOutput(ctx context.Context, sel ast.SelectionSet, v models.RematchRequestOutput) graphql.Marshaler {
	return ec._RematchRequestOutput(ctx, sel, &v)
}

func (ec *executionContext) marshalNRematchRequestOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRematchRequestOutput(ctx context.Context, sel ast.SelectionSet, v *models.RematchRequestOutput) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._RematchRequestOutput(ctx, sel, v)
}

func (ec *executionContext) marshalNSearchUserOutput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSearchUserOutput(ctx context.Context, sel ast.SelectionSet, v models.SearchUserOutput) graphql.Marshaler {
	return ec._SearchUserOutput(ctx, sel, &v)
}

func (ec *executionContext) marshalNSearchUserOutput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSearchUserOutputᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.SearchUserOutput) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNSearchUserOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSearchUserOutput(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNSearchUserOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSearchUserOutput(ctx context.Context, sel ast.SelectionSet, v *models.SearchUserOutput) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._SearchUserOutput(ctx, sel, v)
}

func (ec *executionContext) marshalNStatikCoinLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardEntryᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.StatikCoinLeaderboardEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNStatikCoinLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNStatikCoinLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, v *models.StatikCoinLeaderboardEntry) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._StatikCoinLeaderboardEntry(ctx, sel, v)
}

func (ec *executionContext) marshalNStatikCoinLeaderboardPage2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v models.StatikCoinLeaderboardPage) graphql.Marshaler {
	return ec._StatikCoinLeaderboardPage(ctx, sel, &v)
}

func (ec *executionContext) marshalNStatikCoinLeaderboardPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v *models.StatikCoinLeaderboardPage) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._StatikCoinLeaderboardPage(ctx, sel, v)
}

func (ec *executionContext) marshalNTopPlayerEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayerEntry(ctx context.Context, sel ast.SelectionSet, v []*models.TopPlayerEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOTopPlayerEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayerEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalNTopPlayersLeaderboard2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayersLeaderboard(ctx context.Context, sel ast.SelectionSet, v models.TopPlayersLeaderboard) graphql.Marshaler {
	return ec._TopPlayersLeaderboard(ctx, sel, &v)
}

func (ec *executionContext) marshalNTopPlayersLeaderboard2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayersLeaderboard(ctx context.Context, sel ast.SelectionSet, v *models.TopPlayersLeaderboard) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._TopPlayersLeaderboard(ctx, sel, v)
}

func (ec *executionContext) marshalNUser2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx context.Context, sel ast.SelectionSet, v models.User) graphql.Marshaler {
	return ec._User(ctx, sel, &v)
}

func (ec *executionContext) marshalNUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx context.Context, sel ast.SelectionSet, v *models.User) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._User(ctx, sel, v)
}

func (ec *executionContext) unmarshalNUserActivityType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserActivityType(ctx context.Context, v any) (*models.UserActivityType, error) {
	var res = new(models.UserActivityType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNUserActivityType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserActivityType(ctx context.Context, sel ast.SelectionSet, v *models.UserActivityType) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalNUserDetailWithActivity2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailWithActivityᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.UserDetailWithActivity) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNUserDetailWithActivity2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailWithActivity(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNUserDetailWithActivity2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailWithActivity(ctx context.Context, sel ast.SelectionSet, v *models.UserDetailWithActivity) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserDetailWithActivity(ctx, sel, v)
}

func (ec *executionContext) marshalNUserEvent2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserEvent(ctx context.Context, sel ast.SelectionSet, v models.UserEvent) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserEvent(ctx, sel, v)
}

func (ec *executionContext) marshalNUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetailsᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.UserPublicDetails) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx context.Context, sel ast.SelectionSet, v *models.UserPublicDetails) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserPublicDetails(ctx, sel, v)
}

func (ec *executionContext) marshalNUserRatingFixtureSubmission2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserRatingFixtureSubmission(ctx context.Context, sel ast.SelectionSet, v models.UserRatingFixtureSubmission) graphql.Marshaler {
	return ec._UserRatingFixtureSubmission(ctx, sel, &v)
}

func (ec *executionContext) marshalNUserRatingFixtureSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserRatingFixtureSubmission(ctx context.Context, sel ast.SelectionSet, v *models.UserRatingFixtureSubmission) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserRatingFixtureSubmission(ctx, sel, v)
}

func (ec *executionContext) unmarshalNUserStance2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStance(ctx context.Context, v any) (models.UserStance, error) {
	var res models.UserStance
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNUserStance2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStance(ctx context.Context, sel ast.SelectionSet, v models.UserStance) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNWeeklyLeagueLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueLeaderboardEntryᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.WeeklyLeagueLeaderboardEntry) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNWeeklyLeagueLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueLeaderboardEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNWeeklyLeagueLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, v *models.WeeklyLeagueLeaderboardEntry) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._WeeklyLeagueLeaderboardEntry(ctx, sel, v)
}

func (ec *executionContext) unmarshalOAppleSignInFullNameInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAppleSignInFullNameInput(ctx context.Context, v any) (*models.AppleSignInFullNameInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputAppleSignInFullNameInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOAwardsAndAchievements2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievements(ctx context.Context, sel ast.SelectionSet, v []*models.AwardsAndAchievements) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOAwardsAndAchievements2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievements(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOAwardsAndAchievements2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievements(ctx context.Context, sel ast.SelectionSet, v *models.AwardsAndAchievements) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._AwardsAndAchievements(ctx, sel, v)
}

func (ec *executionContext) unmarshalOAwardsAndAchievementsInput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievementsInput(ctx context.Context, v any) ([]*models.AwardsAndAchievementsInput, error) {
	if v == nil {
		return nil, nil
	}
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]*models.AwardsAndAchievementsInput, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalOAwardsAndAchievementsInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievementsInput(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) unmarshalOAwardsAndAchievementsInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAwardsAndAchievementsInput(ctx context.Context, v any) (*models.AwardsAndAchievementsInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputAwardsAndAchievementsInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOBadgeType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBadgeType(ctx context.Context, v any) (*models.BadgeType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.BadgeType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOBadgeType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBadgeType(ctx context.Context, sel ast.SelectionSet, v *models.BadgeType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalODeviceTokenRegistrationResponse2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDeviceTokenRegistrationResponse(ctx context.Context, sel ast.SelectionSet, v *models.DeviceTokenRegistrationResponse) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._DeviceTokenRegistrationResponse(ctx, sel, v)
}

func (ec *executionContext) marshalOLeaderboardConnection2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardConnection(ctx context.Context, sel ast.SelectionSet, v *models.LeaderboardConnection) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeaderboardConnection(ctx, sel, v)
}

func (ec *executionContext) marshalOLeaderboardEdge2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardEdge(ctx context.Context, sel ast.SelectionSet, v []*models.LeaderboardEdge) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOLeaderboardEdge2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardEdge(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOLeaderboardEdge2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardEdge(ctx context.Context, sel ast.SelectionSet, v *models.LeaderboardEdge) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeaderboardEdge(ctx, sel, v)
}

func (ec *executionContext) marshalOLeagueInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueInfo(ctx context.Context, sel ast.SelectionSet, v *models.LeagueInfo) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeagueInfo(ctx, sel, v)
}

func (ec *executionContext) unmarshalOLeagueType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueType(ctx context.Context, v any) (*models.LeagueType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.LeagueType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOLeagueType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeagueType(ctx context.Context, sel ast.SelectionSet, v *models.LeagueType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOMyInstituteUsersPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMyInstituteUsersPage(ctx context.Context, sel ast.SelectionSet, v *models.MyInstituteUsersPage) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._MyInstituteUsersPage(ctx, sel, v)
}

func (ec *executionContext) marshalOPhoneNumber2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPhoneNumber(ctx context.Context, sel ast.SelectionSet, v *models.PhoneNumber) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PhoneNumber(ctx, sel, v)
}

func (ec *executionContext) unmarshalOStatikCoinLeaderboardType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardType(ctx context.Context, v any) (*models.StatikCoinLeaderboardType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.StatikCoinLeaderboardType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOStatikCoinLeaderboardType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStatikCoinLeaderboardType(ctx context.Context, sel ast.SelectionSet, v *models.StatikCoinLeaderboardType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOTopPlayerEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTopPlayerEntry(ctx context.Context, sel ast.SelectionSet, v *models.TopPlayerEntry) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._TopPlayerEntry(ctx, sel, v)
}

func (ec *executionContext) unmarshalOUpdateUserInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUpdateUserInput(ctx context.Context, v any) (*models.UpdateUserInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputUpdateUserInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx context.Context, sel ast.SelectionSet, v *models.User) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._User(ctx, sel, v)
}

func (ec *executionContext) unmarshalOUserAccountStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAccountStatus(ctx context.Context, v any) (*models.UserAccountStatus, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.UserAccountStatus)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOUserAccountStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAccountStatus(ctx context.Context, sel ast.SelectionSet, v *models.UserAccountStatus) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOUserAdditional2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAdditional(ctx context.Context, sel ast.SelectionSet, v *models.UserAdditional) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserAdditional(ctx, sel, v)
}

func (ec *executionContext) marshalOUserGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserGame(ctx context.Context, sel ast.SelectionSet, v []*models.UserGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOUserGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserGame(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOUserGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserGame(ctx context.Context, sel ast.SelectionSet, v *models.UserGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserGame(ctx, sel, v)
}

func (ec *executionContext) unmarshalOUserInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserInput(ctx context.Context, v any) (*models.UserInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputUserInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOUserLeaderboardPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v *models.UserLeaderboardPage) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserLeaderboardPage(ctx, sel, v)
}

func (ec *executionContext) marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx context.Context, sel ast.SelectionSet, v []*models.UserPublicDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetailsᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.UserPublicDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx context.Context, sel ast.SelectionSet, v *models.UserPublicDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserPublicDetails(ctx, sel, v)
}

func (ec *executionContext) marshalOUserRating2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserRating(ctx context.Context, sel ast.SelectionSet, v *models.UserRating) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserRating(ctx, sel, v)
}

func (ec *executionContext) unmarshalOUserShadowBanStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserShadowBanStatus(ctx context.Context, v any) (*models.UserShadowBanStatus, error) {
	if v == nil {
		return nil, nil
	}
	tmp, err := graphql.UnmarshalString(v)
	res := models.UserShadowBanStatus(tmp)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOUserShadowBanStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserShadowBanStatus(ctx context.Context, sel ast.SelectionSet, v *models.UserShadowBanStatus) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalString(string(*v))
	return res
}

func (ec *executionContext) marshalOUserStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserStats(ctx context.Context, sel ast.SelectionSet, v *models.UserStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserStats(ctx, sel, v)
}

func (ec *executionContext) marshalOUsersWeeklyStatikCoinsOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUsersWeeklyStatikCoinsOutput(ctx context.Context, sel ast.SelectionSet, v *models.UsersWeeklyStatikCoinsOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UsersWeeklyStatikCoinsOutput(ctx, sel, v)
}

func (ec *executionContext) marshalOWeeklyLeagueLeaderboardPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v *models.WeeklyLeagueLeaderboardPage) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._WeeklyLeagueLeaderboardPage(ctx, sel, v)
}

func (ec *executionContext) unmarshalOWeeklyLeagueProgressState2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueProgressState(ctx context.Context, v any) (*models.WeeklyLeagueProgressState, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.WeeklyLeagueProgressState)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOWeeklyLeagueProgressState2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐWeeklyLeagueProgressState(ctx context.Context, sel ast.SelectionSet, v *models.WeeklyLeagueProgressState) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

// endregion ***************************** type.gotpl *****************************
