package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
)

// SendFriendRequest is the resolver for the sendFriendRequest field.
func (r *mutationResolver) SendFriendRequest(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error) {
	return r.FriendsAndFollowersService.SendFriendRequest(ctx, sendRequestInput)
}

// WithdrawFriendRequest is the resolver for the withdrawFriendRequest field.
func (r *mutationResolver) WithdrawFriendRequest(ctx context.Context, withdrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error) {
	return r.FriendsAndFollowersService.WithdrawFriendRequest(ctx, withdrawFriendRequestInput)
}

// AcceptFriendRequest is the resolver for the acceptFriendRequest field.
func (r *mutationResolver) AcceptFriendRequest(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error) {
	return r.FriendsAndFollowersService.AcceptFriendRequest(ctx, acceptRequestInput)
}

// RejectFriendRequest is the resolver for the rejectFriendRequest field.
func (r *mutationResolver) RejectFriendRequest(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error) {
	return r.FriendsAndFollowersService.RejectFriendRequest(ctx, rejectRequestInput)
}

// FollowUser is the resolver for the followFriend field.
func (r *mutationResolver) FollowUser(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error) {
	return r.FriendsAndFollowersService.FollowUser(ctx, followUserInput)
}

// UnFollowUser is the resolver for the unFollowFriend field.
func (r *mutationResolver) UnFollowUser(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error) {
	return r.FriendsAndFollowersService.UnFollowUser(ctx, unFollowUserInput)
}

// RemoveFollower is the resolver for the removeFollower field.
func (r *mutationResolver) RemoveFollower(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error) {
	return r.FriendsAndFollowersService.RemoveFollower(ctx, removeFollowerInput)
}

// RemoveFriend is the resolver for the removeFriend field.
func (r *mutationResolver) RemoveFriend(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error) {
	return r.FriendsAndFollowersService.RemoveFriend(ctx, removeFriendInput)
}

// SendBulkFriendRequestsToInstitute is the resolver for the sendBulkFriendRequestsToInstitute field.
func (r *mutationResolver) SendBulkFriendRequestsToInstitute(ctx context.Context) (int, error) {
	return r.FriendsAndFollowersService.SendBulkFriendRequestsToInstitute(ctx)
}

// GetFollowers is the resolver for the getFollowers field.
func (r *queryResolver) GetFollowers(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	return r.FriendsAndFollowersService.GetFollowers(ctx, page, pageSize)
}

// GetFollowings is the resolver for the getFollowings field.
func (r *queryResolver) GetFollowings(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	return r.FriendsAndFollowersService.GetFollowings(ctx, page, pageSize)
}

// GetFriends is the resolver for the getFriends field.
func (r *queryResolver) GetFriends(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions) (*models.FriendsPage, error) {
	return r.FriendsAndFollowersService.GetFriends(ctx, page, pageSize, sortOption)
}

// GetPendingFriendRequests is the resolver for the getPendingFriendRequests field.
func (r *queryResolver) GetPendingFriendRequests(ctx context.Context, page *int, pageSize *int) (*models.FriendRequestPage, error) {
	return r.FriendsAndFollowersService.GetPendingFriendRequests(ctx, page, pageSize)
}
