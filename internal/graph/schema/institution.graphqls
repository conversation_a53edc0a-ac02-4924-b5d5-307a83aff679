type Institution {
    id: ID!
    name: String!
    logo: String
    domains: [String!]!
    country: String
    state: String
    city: String
    slug: String!
    createdBy:ID!
    createdAt: Time!
    type: InstitutionType!
}

enum InstitutionType {
    SCHOOL
    COLLEGE
    UNIVERSITY
    OTHER
}

input CreateInstitutionInput {
    name: String!
    type: InstitutionType!
    logo: Upload
    domains: [String!]
    country: String
    state: String
    city: String
}

extend type Query {
    searchInstitutions(query: String!, limit: Int = 10): [Institution!]!
}

extend type Mutation {
    createInstitution(input: CreateInstitutionInput!): Institution!
}
