type User {
    _id: ID!
    email: String
    token: String
    name: String
    username: String!
    bio: String
    country: [String]
    links: [String]
    awardsAndAchievements: [AwardsAndAchievements]
    phoneNumber: PhoneNumber
    profileImageUrl: String
    rating: Int
    ratingV2: UserRating
    statikCoins: Int
    badge: BadgeType
    countryCode: String
    isGuest: Boolean
    isBot: Boolean
    isShadowBanned: Boolean
    shadowBanStatus: UserShadowBanStatus
    suspiciousActivity: Boolean
    globalRank: Int
    previousGlobalRank: Int
    countryRank: Int
    previousCountryRank: Int
    stats: UserStats
    hasFixedRating: Boolean
    isSignup: Boolean
    userStreaks: UserStreaks
    timezone: String
    additional: UserAdditional
    league: LeagueInfo
    institutionId: ID
    lastReadFeedId: ID
    referralCode: String
    isReferred: Boolean
    isDeleted: Boolean
    accountStatus: UserAccountStatus
}

enum UserAccountStatus {
    ACTIVE
    INACTIVE
    BANNED
    DELETED
    SUSPENDED
}

enum LeagueType {
    MATIKAN
    RUBY
    DIAMOND
    GOLD
    SILVER
    BRONZE
}

enum UserShadowBanStatus {
    NONE
    PARTIAL
    FULL
}

enum WeeklyLeagueProgressState {
    PROMOTION
    DEMOTION
    NO_CHANGE
}

type LeagueInfo {
    league: LeagueType
    groupId: Int
    updatedAt: Time
    hasParticipated: Boolean
    coinsTillLastWeek: Int
    progressState: WeeklyLeagueProgressState
}

type UserRating {
    globalRating: Int
    flashAnzanRating: Int
    abilityDuelsRating: Int
    puzzleRating: Int
}

type UserAdditional {
    timeSpent: Int
    hasUnlockedAllGames: Boolean
}

type AwardsAndAchievements {
    imageUrl: String
    link: String
    title: String
    description: String
}

enum BadgeType {
    ROOKIE
    NOVICE
    AMATEUR
    EXPERT
    CANDIDATE_MASTER
    MASTER
    GRANDMASTER
    LEGENDARY_GRANDMASTER
    GOAT
}

enum UserActivityType {
    USER_IN_LOBBY
    IN_GAME
    IN_CONTEST
    SEARCHING_FOR_OPPONENT
    PLAYING_DC
    IN_DC_WAITING_ROOM
    VIEWING_DC_LEADERBOARD
    EXPLORING
    PRACTICING_NETS
    SELECTING_CONFIG
    FIXING_RATING
    ONBOARDING
    VIEWING_GAME_RESULT
    ON_CHAT_PAGE
}

type UserPublicDetails {
    _id: ID!
    name: String
    username: String
    profileImageUrl: String
    rating: Int
    badge: BadgeType
    countryCode: String
    isGuest: Boolean
    globalRank: Int
    previousGlobalRank: Int
    countryRank: Int
    previousCountryRank: Int
    stats: UserStats
    userStreaks: UserStreaks
    ratingV2: UserRating
    institutionId: ID
}

type UserStats {
    ngp: Int
    hr: Int
    followersCount: Int
    followingsCount: Int
    friendsCount: Int
    last10BotGames: [UserGame]
    games: [UserGame]
}

type UserGame {
    id: String
    sT: Time
}

type PhoneNumber {
    countryCode: String
    number: String
}

type LeaderboardConnection {
    edges: [LeaderboardEdge]
    pageInfo: PageInfo!
}

type UserLeaderboardPage {
    edges: [LeaderboardEdge]
    totalCount: Int!
}

type LeaderboardEdge {
    cursor: String!
    node: UserPublicDetails!
}

type PageInfo {
    hasNextPage: Boolean!
    endCursor: String
}

input UserInput {
    email: String!
    name: String
    password: String!
    confirm: String!
}

type DeviceTokenRegistrationResponse {
    success: Boolean!
    message: String!
}

type UserRatingFixtureSubmission {
    id: ID!
    userId: ID!
    submissions: [Int!]!
    userScore: Int!
    timeTaken: Int!
    createdAt: Time
    updatedAt: Time
    currentRating: Int!
    proposedRating: Int!
    userStance: UserStance!
}

enum UserStance {
    PENDING
    ACCEPTED
    REJECTED
}

enum UserOnlineStatus {
    ONLINE
    OFFLINE
}

input AwardsAndAchievementsInput {
    imageUrl: String
    link: String
    title: String
    description: String
}

input UpdateUserInput {
    name: String
    profileImageUrl: String
    countryCode: String
    timezone: String
    username: String
    bio: String
    country: [String]
    links: [String]
    awardsAndAchievements: [AwardsAndAchievementsInput]
    institutionId: ID
}

type SearchUserOutput {
    userPublicDetails: User
    isFollowing: Boolean
    friendshipStatus: FRIENDSHIP_STATUS
}

type UserDetailWithActivity {
    userInfo: UserPublicDetails!
    currActivity: UserActivityType!
}

type RatingFixtureOutput {
    newRating: Int
}

type JoinedWeeklyLeagueEvent {
    leagueInfo: LeagueInfo!
}

type OnlineUsersPage {
    users: [UserDetailWithActivity!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

enum StatikCoinLeaderboardType {
    DAILY
    MONTHLY
    WEEKLY
    ALL_TIME
}

type StatikCoinLeaderboardEntry {
    user: UserPublicDetails
    statikCoins: Int
    rank: Int
}

type WeeklyLeagueLeaderboardEntry {
    user: UserPublicDetails
    statikCoins: Int
    rank: Int
    progressState: WeeklyLeagueProgressState
}

type WeeklyLeagueLeaderboardPage {
    results: [WeeklyLeagueLeaderboardEntry!]!
    currentUserLeague: LeagueInfo
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type StatikCoinLeaderboardPage {
    results: [StatikCoinLeaderboardEntry!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type MyInstituteUsersPage {
    results: [SearchUserOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

# Apple SignIn
input AppleSignInFullNameInput {
    familyName: String
    givenName: String
    middleName: String
    namePrefix: String
    nameSuffix: String
    nickname: String
}

input AppleSignInInput {
    authorizationCode: String!
    email: String
    fullName: AppleSignInFullNameInput
    identityToken: String!
    realUserStatus: Int
    state: String
    user: String
}

type TopPlayerEntry {
    user: UserPublicDetails!
    rating: Int!
    rank: Int!
}

type TopPlayersLeaderboard {
    globalRating: [TopPlayerEntry]!
    memoryRating: [TopPlayerEntry]!
    abilityRating: [TopPlayerEntry]!
}

type UsersWeeklyStatikCoinsOutput {
    totalCoins: Int!
    dailyCoins: [Int!]!
}

extend type Query {
    login(email: String!, password: String!): User
    verifyToken(token: String!): User
    getUserById(userId: ID!): User @auth
    getCurrentUser: User @auth
    getUserByUsername(username: String): SearchUserOutput! @auth
    getUserLeagueGroupLeaderboard(
        page: Int = 1
        pageSize: Int = 30
    ): WeeklyLeagueLeaderboardPage @auth
    leaderboardV3(
        countryCode: String
        searchKey: String
        page: Int
        limit: Int
    ): UserLeaderboardPage @auth
    leaderboardNew(
        countryCode: String
        searchKey: String
        page: Int
        limit: Int
        ratingType: String
    ): UserLeaderboardPage @auth
    leaderboard(
        countryCode: String
        searchKey: String
        first: Int
        after: String
    ): LeaderboardConnection @auth
    getRatingFixtureQuestions: [Question!]! @auth
    getRatingFixtureSubmission: UserRatingFixtureSubmission! @auth
    isUsernameAvailable(username: String!): Boolean! @auth
    onlineUsers(page: Int!, pageSize: Int!): OnlineUsersPage! @auth
    statikCoinsLeaderboard(
        page: Int!
        pageSize: Int
        leaderboardType: StatikCoinLeaderboardType
    ): StatikCoinLeaderboardPage! @auth
    getTimeSpentByUser(date: String): Int! @auth @deprecated
    getGlobalTopPlayers: TopPlayersLeaderboard! @auth
    getFriendsLeaderboard(
        page: Int
        pageSize: Int
        ratingType: String
    ): UserLeaderboardPage @auth
    getFriendsTopPlayers: TopPlayersLeaderboard! @auth
    getUsersWeeklyStatikCoins: Int! @auth @deprecated
    getUsersWeeklyStatikCoinsV2(userId: ID!): UsersWeeklyStatikCoinsOutput @auth

    getUsersOfMyInstitute(page: Int, pageSize: Int): MyInstituteUsersPage @auth
    searchUsersInMyInstitute(
        searchKey: String
        page: Int
        pageSize: Int
    ): MyInstituteUsersPage @auth
}

extend type Mutation {
    createUser(userInput: UserInput): User @auth
    loginAsGuest(guestId: ID!): User
    signInWithApple(input: AppleSignInInput!): User!
    googleLogin(
        auth_code: String!
        token_type: String
        expires_in: String
        guestId: ID
    ): User
    legacyGoogleLogin(idToken: String!, guestId: ID): User
    updateUser(updateUserInput: UpdateUserInput): User @auth
    submitRatingFixtureResponses(
        submission: [Int]!
        timeTaken: Int!
    ): UserRatingFixtureSubmission! @auth
    updateRatingBasedOnFixtureResponse(userStance: UserStance!): User! @auth
    uploadProfilePicture(file: Upload!): File! @auth
    submitReferral(referralCode: String!): Boolean! @auth
    deleteUser: Boolean! @auth
}

union UserEvent =
    | RematchRequestOutput
    | BadgeAssignedEvent
    | SearchSubscriptionOutput
    | ChallengeOutput
    | GameCanceledOutput
    | RatingFixtureOutput
    | JoinedWeeklyLeagueEvent

type GameCanceledOutput {
    gameId: String
    creatorId: String
    opponentId: String
    status: ChallengeStatus
}

type RematchRequestOutput {
    gameId: String
    requestedBy: String
    status: String
    newGameId: String
    user: User
    waitingTime: Int
    gameType: String
}

type BadgeAssignedEvent {
    initialBadge: BadgeType
    newBadge: BadgeType
}

extend type Subscription {
    userEvents(userId: ID): UserEvent!
}
